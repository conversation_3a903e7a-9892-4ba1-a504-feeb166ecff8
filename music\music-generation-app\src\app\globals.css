@import "tailwindcss";
@import url('https://use.fontawesome.com/releases/v5.11.2/css/all.css');

:root {
  --background: #0f0f23;
  --foreground: #ffffff;
  --primary: #6366f1;
  --secondary: #8b5cf6;
  --accent: #06b6d4;
  --muted: #374151;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #ffffff;
    --foreground: #1f2937;
    --muted: #f3f4f6;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
}

/* 富文本编辑器样式 */
.ProseMirror {
  outline: none;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #374151;
  background: #1f2937;
  color: #ffffff;
  min-height: 200px;
}

.ProseMirror:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* 音频播放器样式 */
.audio-player {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid #4b5563;
}

.audio-waveform {
  background: linear-gradient(90deg, var(--primary), var(--secondary), var(--accent));
  height: 4px;
  border-radius: 2px;
}

/* 按钮动画 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

/* 卡片样式 */
.card {
  background: rgba(31, 41, 55, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  border-radius: 1rem;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}
