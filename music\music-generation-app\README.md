# AI音乐创作平台

基于人工智能的音乐生成平台，支持歌词创作、风格选择和音频生成。

## 功能特性

### 🎵 核心功能
- **富文本歌词编辑器** - 支持结构化歌词输入，包含 [verse], [chorus], [bridge] 等标签
- **多种音乐风格** - 支持流行、摇滚、民谣、爵士、中国风等12种音乐风格
- **音频参考上传** - 可上传参考音频文件，AI将分析其风格特征
- **智能歌曲描述** - 预设描述选项和自定义文本描述，支持性别、情感、乐器等
- **高级参数调节** - CFG系数、温度、Top-K等参数精细控制
- **音频播放器** - 内置播放器支持播放、暂停、进度控制、音量调节

### 🎨 界面设计
- **现代化UI** - 采用深色主题，科技感十足的视觉设计
- **响应式布局** - 支持桌面端和移动端自适应
- **中文界面** - 全中文用户界面，符合国内用户习惯
- **Font Awesome图标** - 丰富的图标系统提升用户体验

### 🔧 技术栈
- **Next.js 15** - React框架，支持App Router
- **React 19** - 最新版本React
- **Tailwind CSS v4.1** - 原子化CSS框架
- **TypeScript** - 类型安全的JavaScript
- **Tiptap** - 富文本编辑器
- **Axios** - HTTP客户端

## 快速开始

### 环境要求
- Node.js 18.0 或更高版本
- npm, yarn, pnpm 或 bun

### 安装依赖

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 启动开发服务器

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm start
```

## 使用说明

### 1. 歌词创作
- 在左侧的富文本编辑器中输入歌词
- 支持结构化标签：`[intro]`, `[verse]`, `[chorus]`, `[bridge]`, `[outro]`
- 工具栏提供格式化选项：粗体、斜体、列表等

### 2. 风格选择
- 从12种预设音乐风格中选择：流行、摇滚、民谣、爵士、中国风等
- 每种风格都经过优化，适合不同类型的音乐创作

### 3. 音频提示（可选）
- 上传参考音频文件（支持MP3、WAV、FLAC等格式）
- 系统会分析音频的风格、节奏和音色特征
- 最大文件大小：50MB

### 4. 歌曲描述（可选）
- 使用预设选项快速添加描述：性别、情感、风格、节奏、乐器
- 支持自定义英文描述以获得更精确的效果
- 可组合多个预设选项

### 5. 高级设置
- **CFG系数**：控制生成结果与提示的匹配程度（1.0-3.0）
- **温度**：控制生成结果的随机性和创造性（0.1-1.5）
- **Top-K**：限制候选词汇数量，影响生成质量（10-100）

### 6. 生成和播放
- 点击"生成歌曲"按钮开始创作
- 生成过程通常需要1-3分钟
- 完成后可在右侧播放器中试听

## API配置

应用连接到远程Gradio音乐生成服务：

```typescript
// src/lib/musicApi.ts
const GRADIO_API_URL = 'http://************:7860';
```

### API参数说明
- `lyric`: 歌词内容（必需）
- `description`: 歌曲描述（可选）
- `prompt_audio`: 参考音频文件（可选）
- `genre`: 音乐风格
- `cfg_coef`: CFG系数（默认1.5）
- `temperature`: 温度（默认0.9）
- `top_k`: Top-K值（默认50）

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面
├── components/            # React组件
│   ├── AudioPlayer.tsx    # 音频播放器
│   ├── AudioUpload.tsx    # 文件上传
│   ├── RichTextEditor.tsx # 富文本编辑器
│   └── SongDescriptionSelector.tsx # 歌曲描述选择器
└── lib/                   # 工具库
    └── musicApi.ts        # API服务
```

## 开发指南

### 添加新的音乐风格
在 `src/lib/musicApi.ts` 中的 `MUSIC_GENRES` 数组添加新风格：

```typescript
export const MUSIC_GENRES = [
  // ... 现有风格
  'NewGenre'
] as const;
```

### 自定义样式
主要样式定义在 `src/app/globals.css` 中：
- CSS变量定义主题色彩
- 组件样式类
- 响应式设计

### 扩展API功能
在 `src/lib/musicApi.ts` 中的 `MusicApiService` 类添加新方法。

## 故障排除

### 常见问题

1. **音乐生成失败**
   - 检查网络连接
   - 确认远程服务状态
   - 验证歌词格式

2. **音频上传失败**
   - 检查文件格式（支持MP3、WAV、FLAC等）
   - 确认文件大小不超过50MB

3. **页面加载缓慢**
   - 检查网络连接
   - 清除浏览器缓存

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者
