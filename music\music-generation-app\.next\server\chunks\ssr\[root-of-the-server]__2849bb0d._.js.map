{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/nodeWork/music/music-generation-app/src/components/RichTextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEditor, EditorContent } from '@tiptap/react';\nimport StarterKit from '@tiptap/starter-kit';\nimport Placeholder from '@tiptap/extension-placeholder';\nimport CharacterCount from '@tiptap/extension-character-count';\nimport { useEffect } from 'react';\n\ninterface RichTextEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  maxLength?: number;\n  className?: string;\n}\n\nexport default function RichTextEditor({\n  value,\n  onChange,\n  placeholder = \"请输入歌词内容...\",\n  maxLength = 2000,\n  className = \"\"\n}: RichTextEditorProps) {\n  const editor = useEditor({\n    extensions: [\n      StarterKit,\n      Placeholder.configure({\n        placeholder,\n      }),\n      CharacterCount.configure({\n        limit: maxLength,\n      }),\n    ],\n    content: value,\n    onUpdate: ({ editor }) => {\n      onChange(editor.getHTML());\n    },\n    editorProps: {\n      attributes: {\n        class: 'prose prose-invert max-w-none focus:outline-none',\n      },\n    },\n  });\n\n  useEffect(() => {\n    if (editor && editor.getHTML() !== value) {\n      editor.commands.setContent(value);\n    }\n  }, [value, editor]);\n\n  if (!editor) {\n    return null;\n  }\n\n  const characterCount = editor.storage.characterCount.characters();\n  const wordCount = editor.storage.characterCount.words();\n\n  return (\n    <div className={`space-y-3 ${className}`}>\n      {/* 工具栏 */}\n      <div className=\"flex flex-wrap gap-2 p-3 bg-muted/20 rounded-lg border border-muted/30\">\n        <button\n          onClick={() => editor.chain().focus().toggleBold().run()}\n          className={`px-3 py-1 rounded text-sm transition-colors ${\n            editor.isActive('bold')\n              ? 'bg-primary text-white'\n              : 'bg-muted/30 hover:bg-muted/50'\n          }`}\n          type=\"button\"\n        >\n          <i className=\"fas fa-bold\"></i>\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleItalic().run()}\n          className={`px-3 py-1 rounded text-sm transition-colors ${\n            editor.isActive('italic')\n              ? 'bg-primary text-white'\n              : 'bg-muted/30 hover:bg-muted/50'\n          }`}\n          type=\"button\"\n        >\n          <i className=\"fas fa-italic\"></i>\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleStrike().run()}\n          className={`px-3 py-1 rounded text-sm transition-colors ${\n            editor.isActive('strike')\n              ? 'bg-primary text-white'\n              : 'bg-muted/30 hover:bg-muted/50'\n          }`}\n          type=\"button\"\n        >\n          <i className=\"fas fa-strikethrough\"></i>\n        </button>\n        <div className=\"w-px bg-muted/30 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().toggleBulletList().run()}\n          className={`px-3 py-1 rounded text-sm transition-colors ${\n            editor.isActive('bulletList')\n              ? 'bg-primary text-white'\n              : 'bg-muted/30 hover:bg-muted/50'\n          }`}\n          type=\"button\"\n        >\n          <i className=\"fas fa-list-ul\"></i>\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleOrderedList().run()}\n          className={`px-3 py-1 rounded text-sm transition-colors ${\n            editor.isActive('orderedList')\n              ? 'bg-primary text-white'\n              : 'bg-muted/30 hover:bg-muted/50'\n          }`}\n          type=\"button\"\n        >\n          <i className=\"fas fa-list-ol\"></i>\n        </button>\n        <div className=\"w-px bg-muted/30 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().undo().run()}\n          disabled={!editor.can().undo()}\n          className=\"px-3 py-1 rounded text-sm bg-muted/30 hover:bg-muted/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          type=\"button\"\n        >\n          <i className=\"fas fa-undo\"></i>\n        </button>\n        <button\n          onClick={() => editor.chain().focus().redo().run()}\n          disabled={!editor.can().redo()}\n          className=\"px-3 py-1 rounded text-sm bg-muted/30 hover:bg-muted/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          type=\"button\"\n        >\n          <i className=\"fas fa-redo\"></i>\n        </button>\n      </div>\n\n      {/* 编辑器内容 */}\n      <div className=\"relative\">\n        <EditorContent editor={editor} className=\"min-h-[200px]\" />\n      </div>\n\n      {/* 字符统计 */}\n      <div className=\"flex justify-between items-center text-sm text-muted-foreground\">\n        <div className=\"flex gap-4\">\n          <span>字符: {characterCount}/{maxLength}</span>\n          <span>词数: {wordCount}</span>\n        </div>\n        {characterCount > maxLength * 0.9 && (\n          <div className=\"text-yellow-500\">\n            <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n            接近字符限制\n          </div>\n        )}\n      </div>\n\n      {/* 歌词结构提示 */}\n      <div className=\"p-3 bg-accent/10 rounded-lg border border-accent/20\">\n        <div className=\"text-sm text-accent-foreground\">\n          <i className=\"fas fa-info-circle mr-2\"></i>\n          <strong>歌词结构提示：</strong>\n        </div>\n        <div className=\"text-xs text-muted-foreground mt-1 space-y-1\">\n          <div>• 每段代表一个分段，以结构标签开头，以空行结尾</div>\n          <div>• 每行代表一个句子，不建议使用标点符号</div>\n          <div>• 分段标签：[intro], [verse], [chorus], [bridge], [outro]</div>\n          <div>• [intro], [inst], [outro] 不应包含歌词</div>\n          <div>• [verse], [chorus], [bridge] 需要歌词内容</div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAgBe,SAAS,eAAe,EACrC,KAAK,EACL,QAAQ,EACR,cAAc,YAAY,EAC1B,YAAY,IAAI,EAChB,YAAY,EAAE,EACM;IACpB,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD,EAAE;QACvB,YAAY;YACV,2JAAA,CAAA,UAAU;YACV,qKAAA,CAAA,UAAW,CAAC,SAAS,CAAC;gBACpB;YACF;YACA,4KAAA,CAAA,UAAc,CAAC,SAAS,CAAC;gBACvB,OAAO;YACT;SACD;QACD,SAAS;QACT,UAAU,CAAC,EAAE,MAAM,EAAE;YACnB,SAAS,OAAO,OAAO;QACzB;QACA,aAAa;YACX,YAAY;gBACV,OAAO;YACT;QACF;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,OAAO,OAAO,OAAO,OAAO;YACxC,OAAO,QAAQ,CAAC,UAAU,CAAC;QAC7B;IACF,GAAG;QAAC;QAAO;KAAO;IAElB,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,MAAM,iBAAiB,OAAO,OAAO,CAAC,cAAc,CAAC,UAAU;IAC/D,MAAM,YAAY,OAAO,OAAO,CAAC,cAAc,CAAC,KAAK;IAErD,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;wBACtD,WAAW,CAAC,4CAA4C,EACtD,OAAO,QAAQ,CAAC,UACZ,0BACA,iCACJ;wBACF,MAAK;kCAEL,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,8OAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,WAAW,CAAC,4CAA4C,EACtD,OAAO,QAAQ,CAAC,YACZ,0BACA,iCACJ;wBACF,MAAK;kCAEL,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,8OAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,WAAW,CAAC,4CAA4C,EACtD,OAAO,QAAQ,CAAC,YACZ,0BACA,iCACJ;wBACF,MAAK;kCAEL,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAW,CAAC,4CAA4C,EACtD,OAAO,QAAQ,CAAC,gBACZ,0BACA,iCACJ;wBACF,MAAK;kCAEL,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,8OAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,iBAAiB,GAAG,GAAG;wBAC7D,WAAW,CAAC,4CAA4C,EACtD,OAAO,QAAQ,CAAC,iBACZ,0BACA,iCACJ;wBACF,MAAK;kCAEL,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAChD,UAAU,CAAC,OAAO,GAAG,GAAG,IAAI;wBAC5B,WAAU;wBACV,MAAK;kCAEL,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,8OAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAChD,UAAU,CAAC,OAAO,GAAG,GAAG,IAAI;wBAC5B,WAAU;wBACV,MAAK;kCAEL,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;0BAKjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kKAAA,CAAA,gBAAa;oBAAC,QAAQ;oBAAQ,WAAU;;;;;;;;;;;0BAI3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAK;oCAAK;oCAAe;oCAAE;;;;;;;0CAC5B,8OAAC;;oCAAK;oCAAK;;;;;;;;;;;;;oBAEZ,iBAAiB,YAAY,qBAC5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;4BAAuC;;;;;;;;;;;;;0BAO1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;0CAAO;;;;;;;;;;;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;;;;;;;;;;;;;;;;;;;AAKf", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/nodeWork/music/music-generation-app/src/components/AudioUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useCallback } from 'react';\n\ninterface AudioUploadProps {\n  onFileSelect: (file: File | null) => void;\n  accept?: string;\n  maxSize?: number; // in MB\n  className?: string;\n}\n\nexport default function AudioUpload({\n  onFileSelect,\n  accept = \"audio/*\",\n  maxSize = 10,\n  className = \"\"\n}: AudioUploadProps) {\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [error, setError] = useState<string>(\"\");\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const validateFile = (file: File): string | null => {\n    // 检查文件类型\n    if (!file.type.startsWith('audio/')) {\n      return '请选择音频文件';\n    }\n\n    // 检查文件大小\n    const fileSizeMB = file.size / (1024 * 1024);\n    if (fileSizeMB > maxSize) {\n      return `文件大小不能超过 ${maxSize}MB`;\n    }\n\n    return null;\n  };\n\n  const handleFileSelect = useCallback((file: File) => {\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      setSelectedFile(null);\n      onFileSelect(null);\n      return;\n    }\n\n    setError(\"\");\n    setSelectedFile(file);\n    onFileSelect(file);\n  }, [maxSize, onFileSelect]);\n\n  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  const handleDragOver = (event: React.DragEvent) => {\n    event.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (event: React.DragEvent) => {\n    event.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (event: React.DragEvent) => {\n    event.preventDefault();\n    setIsDragOver(false);\n\n    const files = event.dataTransfer.files;\n    if (files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  };\n\n  const handleRemoveFile = () => {\n    setSelectedFile(null);\n    setError(\"\");\n    onFileSelect(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = \"\";\n    }\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const formatDuration = (duration: number): string => {\n    const minutes = Math.floor(duration / 60);\n    const seconds = Math.floor(duration % 60);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* 上传区域 */}\n      <div\n        className={`\n          relative border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200\n          ${isDragOver \n            ? 'border-primary bg-primary/5' \n            : selectedFile \n              ? 'border-green-500 bg-green-500/5' \n              : 'border-muted hover:border-primary/50 hover:bg-primary/5'\n          }\n          ${error ? 'border-red-500 bg-red-500/5' : ''}\n        `}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          accept={accept}\n          onChange={handleFileInputChange}\n          className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n        />\n\n        {selectedFile ? (\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-green-500/20 rounded-full\">\n              <i className=\"fas fa-music text-2xl text-green-500\"></i>\n            </div>\n            <div>\n              <p className=\"font-medium text-green-600\">{selectedFile.name}</p>\n              <p className=\"text-sm text-muted-foreground\">\n                {formatFileSize(selectedFile.size)}\n              </p>\n            </div>\n            <button\n              onClick={handleRemoveFile}\n              className=\"inline-flex items-center px-3 py-1 text-sm bg-red-500/20 text-red-600 rounded-md hover:bg-red-500/30 transition-colors\"\n            >\n              <i className=\"fas fa-trash mr-1\"></i>\n              移除文件\n            </button>\n          </div>\n        ) : (\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-muted/20 rounded-full\">\n              <i className={`fas fa-cloud-upload-alt text-2xl ${isDragOver ? 'text-primary' : 'text-muted-foreground'}`}></i>\n            </div>\n            <div>\n              <p className=\"text-lg font-medium\">\n                {isDragOver ? '释放文件以上传' : '拖拽音频文件到此处'}\n              </p>\n              <p className=\"text-sm text-muted-foreground\">\n                或 <span className=\"text-primary cursor-pointer hover:underline\">点击选择文件</span>\n              </p>\n            </div>\n            <div className=\"text-xs text-muted-foreground\">\n              支持格式：MP3, WAV, FLAC, AAC 等 • 最大 {maxSize}MB\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 错误信息 */}\n      {error && (\n        <div className=\"flex items-center p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-600\">\n          <i className=\"fas fa-exclamation-circle mr-2\"></i>\n          {error}\n        </div>\n      )}\n\n      {/* 上传提示 */}\n      <div className=\"p-3 bg-accent/10 rounded-lg border border-accent/20\">\n        <div className=\"text-sm text-accent-foreground\">\n          <i className=\"fas fa-info-circle mr-2\"></i>\n          <strong>音频提示说明：</strong>\n        </div>\n        <div className=\"text-xs text-muted-foreground mt-1 space-y-1\">\n          <div>• 上传的音频文件将作为生成音乐的参考</div>\n          <div>• 系统会分析音频的风格、节奏和音色特征</div>\n          <div>• 建议上传高质量的音频文件以获得更好的效果</div>\n          <div>• 音频时长建议在30秒到5分钟之间</div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAWe,SAAS,YAAY,EAClC,YAAY,EACZ,SAAS,SAAS,EAClB,UAAU,EAAE,EACZ,YAAY,EAAE,EACG;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,eAAe,CAAC;QACpB,SAAS;QACT,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,OAAO;QACT;QAEA,SAAS;QACT,MAAM,aAAa,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;QAC3C,IAAI,aAAa,SAAS;YACxB,OAAO,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC;QAChC;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,kBAAkB,aAAa;QACrC,IAAI,iBAAiB;YACnB,SAAS;YACT,gBAAgB;YAChB,aAAa;YACb;QACF;QAEA,SAAS;QACT,gBAAgB;QAChB,aAAa;IACf,GAAG;QAAC;QAAS;KAAa;IAE1B,MAAM,wBAAwB,CAAC;QAC7B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc;QACpB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,cAAc;QACpB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;QACpB,cAAc;QAEd,MAAM,QAAQ,MAAM,YAAY,CAAC,KAAK;QACtC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,SAAS;QACT,aAAa;QACb,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,KAAK,KAAK,CAAC,WAAW;QACtC,MAAM,UAAU,KAAK,KAAK,CAAC,WAAW;QACtC,OAAO,GAAG,QAAQ,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC5D;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBACC,WAAW,CAAC;;UAEV,EAAE,aACE,gCACA,eACE,oCACA,0DACL;UACD,EAAE,QAAQ,gCAAgC,GAAG;QAC/C,CAAC;gBACD,YAAY;gBACZ,aAAa;gBACb,QAAQ;;kCAER,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,UAAU;wBACV,WAAU;;;;;;oBAGX,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA8B,aAAa,IAAI;;;;;;kDAC5D,8OAAC;wCAAE,WAAU;kDACV,eAAe,aAAa,IAAI;;;;;;;;;;;;0CAGrC,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAAwB;;;;;;;;;;;;6CAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAW,CAAC,iCAAiC,EAAE,aAAa,iBAAiB,yBAAyB;;;;;;;;;;;0CAE3G,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDACV,aAAa,YAAY;;;;;;kDAE5B,8OAAC;wCAAE,WAAU;;4CAAgC;0DACzC,8OAAC;gDAAK,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;0CAGpE,8OAAC;gCAAI,WAAU;;oCAAgC;oCACZ;oCAAQ;;;;;;;;;;;;;;;;;;;YAOhD,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;0CAAO;;;;;;;;;;;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;;;;;;;;;;;;;;;;;;;AAKf", "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/nodeWork/music/music-generation-app/src/components/SongDescriptionSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SongDescriptionSelectorProps {\n  value: string;\n  onChange: (value: string) => void;\n  className?: string;\n}\n\n// 预设的歌曲描述选项\nconst PRESET_DESCRIPTIONS = {\n  gender: {\n    title: '性别',\n    icon: 'fas fa-user',\n    options: [\n      { label: '男声', value: 'male vocals', description: '男性歌手演唱' },\n      { label: '女声', value: 'female vocals', description: '女性歌手演唱' },\n      { label: '混合', value: 'mixed vocals', description: '男女混合演唱' },\n      { label: '童声', value: 'children vocals', description: '儿童歌手演唱' },\n    ]\n  },\n  mood: {\n    title: '情感',\n    icon: 'fas fa-heart',\n    options: [\n      { label: '快乐', value: 'happy, upbeat, joyful', description: '欢快愉悦的情感' },\n      { label: '悲伤', value: 'sad, melancholic, emotional', description: '悲伤忧郁的情感' },\n      { label: '浪漫', value: 'romantic, love, tender', description: '浪漫温柔的情感' },\n      { label: '激昂', value: 'energetic, powerful, passionate', description: '激情澎湃的情感' },\n      { label: '平静', value: 'calm, peaceful, serene', description: '平静安详的情感' },\n      { label: '神秘', value: 'mysterious, dark, atmospheric', description: '神秘深邃的情感' },\n    ]\n  },\n  style: {\n    title: '风格',\n    icon: 'fas fa-music',\n    options: [\n      { label: '流行', value: 'pop, mainstream, catchy', description: '流行音乐风格' },\n      { label: '摇滚', value: 'rock, electric guitar, drums', description: '摇滚音乐风格' },\n      { label: '民谣', value: 'folk, acoustic, storytelling', description: '民谣音乐风格' },\n      { label: '电子', value: 'electronic, synthesizer, digital', description: '电子音乐风格' },\n      { label: '爵士', value: 'jazz, swing, improvisation', description: '爵士音乐风格' },\n      { label: '古典', value: 'classical, orchestral, elegant', description: '古典音乐风格' },\n      { label: '中国风', value: 'chinese traditional, oriental, cultural', description: '中国传统风格' },\n    ]\n  },\n  tempo: {\n    title: '节奏',\n    icon: 'fas fa-tachometer-alt',\n    options: [\n      { label: '慢板', value: 'slow tempo, 60-80 BPM', description: '缓慢的节奏' },\n      { label: '中板', value: 'medium tempo, 90-120 BPM', description: '中等的节奏' },\n      { label: '快板', value: 'fast tempo, 130-160 BPM', description: '快速的节奏' },\n      { label: '极快', value: 'very fast tempo, 170+ BPM', description: '非常快的节奏' },\n    ]\n  },\n  instruments: {\n    title: '乐器',\n    icon: 'fas fa-guitar',\n    options: [\n      { label: '钢琴', value: 'piano, keys', description: '钢琴演奏' },\n      { label: '吉他', value: 'guitar, acoustic guitar', description: '吉他演奏' },\n      { label: '小提琴', value: 'violin, strings', description: '小提琴演奏' },\n      { label: '鼓组', value: 'drums, percussion', description: '鼓组节奏' },\n      { label: '贝斯', value: 'bass, bass guitar', description: '贝斯低音' },\n      { label: '合成器', value: 'synthesizer, electronic', description: '合成器音色' },\n      { label: '管弦乐', value: 'orchestra, symphonic', description: '管弦乐团' },\n      { label: '中国乐器', value: 'chinese instruments, erhu, guzheng', description: '中国传统乐器' },\n    ]\n  }\n};\n\nexport default function SongDescriptionSelector({\n  value,\n  onChange,\n  className = \"\"\n}: SongDescriptionSelectorProps) {\n  const [selectedPresets, setSelectedPresets] = useState<string[]>([]);\n\n  const handlePresetToggle = (presetValue: string) => {\n    const newPresets = selectedPresets.includes(presetValue)\n      ? selectedPresets.filter(p => p !== presetValue)\n      : [...selectedPresets, presetValue];\n    \n    setSelectedPresets(newPresets);\n    \n    // 更新描述文本\n    const newDescription = newPresets.join(', ');\n    onChange(newDescription);\n  };\n\n  const handleTextChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {\n    onChange(event.target.value);\n    // 清空预设选择状态，因为用户手动编辑了\n    setSelectedPresets([]);\n  };\n\n  const insertPreset = (presetValue: string) => {\n    const newValue = value ? `${value}, ${presetValue}` : presetValue;\n    onChange(newValue);\n  };\n\n  const clearAll = () => {\n    onChange('');\n    setSelectedPresets([]);\n  };\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* 预设选项 */}\n      <div className=\"space-y-4\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold flex items-center\">\n            <i className=\"fas fa-palette mr-2 text-primary\"></i>\n            预设描述选项\n          </h3>\n          <button\n            onClick={clearAll}\n            className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n          >\n            <i className=\"fas fa-eraser mr-1\"></i>\n            清空所有\n          </button>\n        </div>\n\n        {Object.entries(PRESET_DESCRIPTIONS).map(([key, category]) => (\n          <div key={key} className=\"space-y-2\">\n            <h4 className=\"text-sm font-medium text-muted-foreground flex items-center\">\n              <i className={`${category.icon} mr-2`}></i>\n              {category.title}\n            </h4>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2\">\n              {category.options.map((option) => (\n                <button\n                  key={option.value}\n                  onClick={() => insertPreset(option.value)}\n                  className=\"p-3 text-left bg-muted/20 hover:bg-muted/40 rounded-lg border border-muted/30 hover:border-primary/50 transition-all duration-200 group\"\n                  title={option.description}\n                >\n                  <div className=\"text-sm font-medium group-hover:text-primary transition-colors\">\n                    {option.label}\n                  </div>\n                  <div className=\"text-xs text-muted-foreground mt-1\">\n                    {option.description}\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* 自定义文本输入 */}\n      <div className=\"space-y-3\">\n        <label className=\"text-sm font-medium flex items-center\">\n          <i className=\"fas fa-edit mr-2 text-primary\"></i>\n          自定义描述 (英文)\n        </label>\n        <textarea\n          value={value}\n          onChange={handleTextChange}\n          placeholder=\"描述歌曲的性别、音色、风格、情感、乐器和BPM。例如：female vocals, pop, happy, piano, 120 BPM\"\n          className=\"w-full h-24 p-3 bg-muted/20 border border-muted/30 rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all resize-none\"\n        />\n        <div className=\"text-xs text-muted-foreground\">\n          字符数: {value.length} • 建议使用英文描述以获得更好的效果\n        </div>\n      </div>\n\n      {/* 使用提示 */}\n      <div className=\"p-4 bg-accent/10 rounded-lg border border-accent/20\">\n        <div className=\"text-sm text-accent-foreground\">\n          <i className=\"fas fa-lightbulb mr-2\"></i>\n          <strong>使用提示：</strong>\n        </div>\n        <div className=\"text-xs text-muted-foreground mt-2 space-y-1\">\n          <div>• 点击预设选项可快速插入常用描述</div>\n          <div>• 可以组合多个预设选项创建复合描述</div>\n          <div>• 支持手动编辑和自定义描述内容</div>\n          <div>• 建议包含：性别、风格、情感、主要乐器、节奏等信息</div>\n          <div>• 使用英文描述可以获得更准确的生成效果</div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUA,YAAY;AACZ,MAAM,sBAAsB;IAC1B,QAAQ;QACN,OAAO;QACP,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAM,OAAO;gBAAe,aAAa;YAAS;YAC3D;gBAAE,OAAO;gBAAM,OAAO;gBAAiB,aAAa;YAAS;YAC7D;gBAAE,OAAO;gBAAM,OAAO;gBAAgB,aAAa;YAAS;YAC5D;gBAAE,OAAO;gBAAM,OAAO;gBAAmB,aAAa;YAAS;SAChE;IACH;IACA,MAAM;QACJ,OAAO;QACP,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAM,OAAO;gBAAyB,aAAa;YAAU;YACtE;gBAAE,OAAO;gBAAM,OAAO;gBAA+B,aAAa;YAAU;YAC5E;gBAAE,OAAO;gBAAM,OAAO;gBAA0B,aAAa;YAAU;YACvE;gBAAE,OAAO;gBAAM,OAAO;gBAAmC,aAAa;YAAU;YAChF;gBAAE,OAAO;gBAAM,OAAO;gBAA0B,aAAa;YAAU;YACvE;gBAAE,OAAO;gBAAM,OAAO;gBAAiC,aAAa;YAAU;SAC/E;IACH;IACA,OAAO;QACL,OAAO;QACP,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAM,OAAO;gBAA2B,aAAa;YAAS;YACvE;gBAAE,OAAO;gBAAM,OAAO;gBAAgC,aAAa;YAAS;YAC5E;gBAAE,OAAO;gBAAM,OAAO;gBAAgC,aAAa;YAAS;YAC5E;gBAAE,OAAO;gBAAM,OAAO;gBAAoC,aAAa;YAAS;YAChF;gBAAE,OAAO;gBAAM,OAAO;gBAA8B,aAAa;YAAS;YAC1E;gBAAE,OAAO;gBAAM,OAAO;gBAAkC,aAAa;YAAS;YAC9E;gBAAE,OAAO;gBAAO,OAAO;gBAA2C,aAAa;YAAS;SACzF;IACH;IACA,OAAO;QACL,OAAO;QACP,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAM,OAAO;gBAAyB,aAAa;YAAQ;YACpE;gBAAE,OAAO;gBAAM,OAAO;gBAA4B,aAAa;YAAQ;YACvE;gBAAE,OAAO;gBAAM,OAAO;gBAA2B,aAAa;YAAQ;YACtE;gBAAE,OAAO;gBAAM,OAAO;gBAA6B,aAAa;YAAS;SAC1E;IACH;IACA,aAAa;QACX,OAAO;QACP,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAM,OAAO;gBAAe,aAAa;YAAO;YACzD;gBAAE,OAAO;gBAAM,OAAO;gBAA2B,aAAa;YAAO;YACrE;gBAAE,OAAO;gBAAO,OAAO;gBAAmB,aAAa;YAAQ;YAC/D;gBAAE,OAAO;gBAAM,OAAO;gBAAqB,aAAa;YAAO;YAC/D;gBAAE,OAAO;gBAAM,OAAO;gBAAqB,aAAa;YAAO;YAC/D;gBAAE,OAAO;gBAAO,OAAO;gBAA2B,aAAa;YAAQ;YACvE;gBAAE,OAAO;gBAAO,OAAO;gBAAwB,aAAa;YAAO;YACnE;gBAAE,OAAO;gBAAQ,OAAO;gBAAsC,aAAa;YAAS;SACrF;IACH;AACF;AAEe,SAAS,wBAAwB,EAC9C,KAAK,EACL,QAAQ,EACR,YAAY,EAAE,EACe;IAC7B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnE,MAAM,qBAAqB,CAAC;QAC1B,MAAM,aAAa,gBAAgB,QAAQ,CAAC,eACxC,gBAAgB,MAAM,CAAC,CAAA,IAAK,MAAM,eAClC;eAAI;YAAiB;SAAY;QAErC,mBAAmB;QAEnB,SAAS;QACT,MAAM,iBAAiB,WAAW,IAAI,CAAC;QACvC,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS,MAAM,MAAM,CAAC,KAAK;QAC3B,qBAAqB;QACrB,mBAAmB,EAAE;IACvB;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,QAAQ,GAAG,MAAM,EAAE,EAAE,aAAa,GAAG;QACtD,SAAS;IACX;IAEA,MAAM,WAAW;QACf,SAAS;QACT,mBAAmB,EAAE;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAE,WAAU;;;;;;oCAAuC;;;;;;;0CAGtD,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAAyB;;;;;;;;;;;;;oBAKzC,OAAO,OAAO,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS,iBACvD,8OAAC;4BAAc,WAAU;;8CACvB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAE,WAAW,GAAG,SAAS,IAAI,CAAC,KAAK,CAAC;;;;;;wCACpC,SAAS,KAAK;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,uBACrB,8OAAC;4CAEC,SAAS,IAAM,aAAa,OAAO,KAAK;4CACxC,WAAU;4CACV,OAAO,OAAO,WAAW;;8DAEzB,8OAAC;oDAAI,WAAU;8DACZ,OAAO,KAAK;;;;;;8DAEf,8OAAC;oDAAI,WAAU;8DACZ,OAAO,WAAW;;;;;;;2CAThB,OAAO,KAAK;;;;;;;;;;;2BARf;;;;;;;;;;;0BA2Bd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCAAE,WAAU;;;;;;4BAAoC;;;;;;;kCAGnD,8OAAC;wBACC,OAAO;wBACP,UAAU;wBACV,aAAY;wBACZ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;4BAAgC;4BACvC,MAAM,MAAM;4BAAC;;;;;;;;;;;;;0BAKvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;0CAAO;;;;;;;;;;;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;;;;;;;;;;;;;;;;;;;AAKf", "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/nodeWork/music/music-generation-app/src/components/AudioPlayer.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\n\ninterface AudioPlayerProps {\n  src?: string;\n  title?: string;\n  artist?: string;\n  onLoadStart?: () => void;\n  onLoadEnd?: () => void;\n  onError?: (error: string) => void;\n  className?: string;\n}\n\nexport default function AudioPlayer({\n  src,\n  title = \"生成的音乐\",\n  artist = \"AI音乐创作\",\n  onLoadStart,\n  onLoadEnd,\n  onError,\n  className = \"\"\n}: AudioPlayerProps) {\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [currentTime, setCurrentTime] = useState(0);\n  const [duration, setDuration] = useState(0);\n  const [volume, setVolume] = useState(1);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string>(\"\");\n  \n  const audioRef = useRef<HTMLAudioElement>(null);\n  const progressRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    const handleLoadStart = () => {\n      setIsLoading(true);\n      setError(\"\");\n      onLoadStart?.();\n    };\n\n    const handleLoadedData = () => {\n      setIsLoading(false);\n      setDuration(audio.duration);\n      onLoadEnd?.();\n    };\n\n    const handleTimeUpdate = () => {\n      setCurrentTime(audio.currentTime);\n    };\n\n    const handleEnded = () => {\n      setIsPlaying(false);\n      setCurrentTime(0);\n    };\n\n    const handleError = () => {\n      setIsLoading(false);\n      const errorMsg = \"音频加载失败\";\n      setError(errorMsg);\n      onError?.(errorMsg);\n    };\n\n    audio.addEventListener('loadstart', handleLoadStart);\n    audio.addEventListener('loadeddata', handleLoadedData);\n    audio.addEventListener('timeupdate', handleTimeUpdate);\n    audio.addEventListener('ended', handleEnded);\n    audio.addEventListener('error', handleError);\n\n    return () => {\n      audio.removeEventListener('loadstart', handleLoadStart);\n      audio.removeEventListener('loadeddata', handleLoadedData);\n      audio.removeEventListener('timeupdate', handleTimeUpdate);\n      audio.removeEventListener('ended', handleEnded);\n      audio.removeEventListener('error', handleError);\n    };\n  }, [onLoadStart, onLoadEnd, onError]);\n\n  const togglePlay = () => {\n    const audio = audioRef.current;\n    if (!audio || !src) return;\n\n    if (isPlaying) {\n      audio.pause();\n    } else {\n      audio.play();\n    }\n    setIsPlaying(!isPlaying);\n  };\n\n  const handleProgressClick = (event: React.MouseEvent<HTMLDivElement>) => {\n    const audio = audioRef.current;\n    const progressBar = progressRef.current;\n    if (!audio || !progressBar || !duration) return;\n\n    const rect = progressBar.getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const newTime = (clickX / rect.width) * duration;\n    \n    audio.currentTime = newTime;\n    setCurrentTime(newTime);\n  };\n\n  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const newVolume = parseFloat(event.target.value);\n    setVolume(newVolume);\n    \n    const audio = audioRef.current;\n    if (audio) {\n      audio.volume = newVolume;\n    }\n  };\n\n  const formatTime = (time: number): string => {\n    if (isNaN(time)) return \"0:00\";\n    \n    const minutes = Math.floor(time / 60);\n    const seconds = Math.floor(time % 60);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  };\n\n  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;\n\n  if (!src) {\n    return (\n      <div className={`audio-player p-6 text-center ${className}`}>\n        <div className=\"flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-muted/20 rounded-full\">\n          <i className=\"fas fa-music text-2xl text-muted-foreground\"></i>\n        </div>\n        <p className=\"text-muted-foreground\">暂无音频文件</p>\n        <p className=\"text-sm text-muted-foreground mt-1\">生成音乐后将在此处播放</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`audio-player p-6 ${className}`}>\n      <audio ref={audioRef} src={src} preload=\"metadata\" />\n      \n      {error && (\n        <div className=\"mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-600 text-sm\">\n          <i className=\"fas fa-exclamation-circle mr-2\"></i>\n          {error}\n        </div>\n      )}\n\n      {/* 音乐信息 */}\n      <div className=\"flex items-center mb-6\">\n        <div className=\"flex items-center justify-center w-12 h-12 bg-primary/20 rounded-lg mr-4\">\n          <i className=\"fas fa-music text-primary\"></i>\n        </div>\n        <div className=\"flex-1\">\n          <h3 className=\"font-semibold text-lg\">{title}</h3>\n          <p className=\"text-sm text-muted-foreground\">{artist}</p>\n        </div>\n        <div className=\"text-sm text-muted-foreground\">\n          {formatTime(currentTime)} / {formatTime(duration)}\n        </div>\n      </div>\n\n      {/* 进度条 */}\n      <div className=\"mb-6\">\n        <div\n          ref={progressRef}\n          className=\"relative h-2 bg-muted/30 rounded-full cursor-pointer group\"\n          onClick={handleProgressClick}\n        >\n          <div\n            className=\"absolute top-0 left-0 h-full bg-gradient-to-r from-primary to-secondary rounded-full transition-all duration-150\"\n            style={{ width: `${progressPercentage}%` }}\n          />\n          <div\n            className=\"absolute top-1/2 w-4 h-4 bg-white border-2 border-primary rounded-full transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity shadow-lg\"\n            style={{ left: `calc(${progressPercentage}% - 8px)` }}\n          />\n        </div>\n      </div>\n\n      {/* 控制按钮 */}\n      <div className=\"flex items-center justify-center space-x-4 mb-4\">\n        <button\n          onClick={() => {\n            const audio = audioRef.current;\n            if (audio) {\n              audio.currentTime = Math.max(0, audio.currentTime - 10);\n            }\n          }}\n          className=\"p-2 rounded-full bg-muted/20 hover:bg-muted/40 transition-colors\"\n          disabled={!src || isLoading}\n        >\n          <i className=\"fas fa-backward text-lg\"></i>\n        </button>\n\n        <button\n          onClick={togglePlay}\n          disabled={!src || isLoading}\n          className=\"p-4 rounded-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isLoading ? (\n            <i className=\"fas fa-spinner fa-spin text-xl\"></i>\n          ) : isPlaying ? (\n            <i className=\"fas fa-pause text-xl\"></i>\n          ) : (\n            <i className=\"fas fa-play text-xl\"></i>\n          )}\n        </button>\n\n        <button\n          onClick={() => {\n            const audio = audioRef.current;\n            if (audio) {\n              audio.currentTime = Math.min(duration, audio.currentTime + 10);\n            }\n          }}\n          className=\"p-2 rounded-full bg-muted/20 hover:bg-muted/40 transition-colors\"\n          disabled={!src || isLoading}\n        >\n          <i className=\"fas fa-forward text-lg\"></i>\n        </button>\n      </div>\n\n      {/* 音量控制 */}\n      <div className=\"flex items-center space-x-3\">\n        <i className=\"fas fa-volume-down text-muted-foreground\"></i>\n        <input\n          type=\"range\"\n          min=\"0\"\n          max=\"1\"\n          step=\"0.1\"\n          value={volume}\n          onChange={handleVolumeChange}\n          className=\"flex-1 h-1 bg-muted/30 rounded-lg appearance-none cursor-pointer\"\n        />\n        <i className=\"fas fa-volume-up text-muted-foreground\"></i>\n        <span className=\"text-sm text-muted-foreground w-8\">\n          {Math.round(volume * 100)}%\n        </span>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAce,SAAS,YAAY,EAClC,GAAG,EACH,QAAQ,OAAO,EACf,SAAS,QAAQ,EACjB,WAAW,EACX,SAAS,EACT,OAAO,EACP,YAAY,EAAE,EACG;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,kBAAkB;YACtB,aAAa;YACb,SAAS;YACT;QACF;QAEA,MAAM,mBAAmB;YACvB,aAAa;YACb,YAAY,MAAM,QAAQ;YAC1B;QACF;QAEA,MAAM,mBAAmB;YACvB,eAAe,MAAM,WAAW;QAClC;QAEA,MAAM,cAAc;YAClB,aAAa;YACb,eAAe;QACjB;QAEA,MAAM,cAAc;YAClB,aAAa;YACb,MAAM,WAAW;YACjB,SAAS;YACT,UAAU;QACZ;QAEA,MAAM,gBAAgB,CAAC,aAAa;QACpC,MAAM,gBAAgB,CAAC,cAAc;QACrC,MAAM,gBAAgB,CAAC,cAAc;QACrC,MAAM,gBAAgB,CAAC,SAAS;QAChC,MAAM,gBAAgB,CAAC,SAAS;QAEhC,OAAO;YACL,MAAM,mBAAmB,CAAC,aAAa;YACvC,MAAM,mBAAmB,CAAC,cAAc;YACxC,MAAM,mBAAmB,CAAC,cAAc;YACxC,MAAM,mBAAmB,CAAC,SAAS;YACnC,MAAM,mBAAmB,CAAC,SAAS;QACrC;IACF,GAAG;QAAC;QAAa;QAAW;KAAQ;IAEpC,MAAM,aAAa;QACjB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,SAAS,CAAC,KAAK;QAEpB,IAAI,WAAW;YACb,MAAM,KAAK;QACb,OAAO;YACL,MAAM,IAAI;QACZ;QACA,aAAa,CAAC;IAChB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,cAAc,YAAY,OAAO;QACvC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU;QAEzC,MAAM,OAAO,YAAY,qBAAqB;QAC9C,MAAM,SAAS,MAAM,OAAO,GAAG,KAAK,IAAI;QACxC,MAAM,UAAU,AAAC,SAAS,KAAK,KAAK,GAAI;QAExC,MAAM,WAAW,GAAG;QACpB,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,YAAY,WAAW,MAAM,MAAM,CAAC,KAAK;QAC/C,UAAU;QAEV,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,OAAO;YACT,MAAM,MAAM,GAAG;QACjB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,MAAM,OAAO,OAAO;QAExB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,OAAO,GAAG,QAAQ,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC5D;IAEA,MAAM,qBAAqB,WAAW,IAAI,AAAC,cAAc,WAAY,MAAM;IAE3E,IAAI,CAAC,KAAK;QACR,qBACE,8OAAC;YAAI,WAAW,CAAC,6BAA6B,EAAE,WAAW;;8BACzD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;;;;;;;;;;8BAEf,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;8BACrC,8OAAC;oBAAE,WAAU;8BAAqC;;;;;;;;;;;;IAGxD;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW;;0BAC7C,8OAAC;gBAAM,KAAK;gBAAU,KAAK;gBAAK,SAAQ;;;;;;YAEvC,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,8OAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;kCAEhD,8OAAC;wBAAI,WAAU;;4BACZ,WAAW;4BAAa;4BAAI,WAAW;;;;;;;;;;;;;0BAK5C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,KAAK;oBACL,WAAU;oBACV,SAAS;;sCAET,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;4BAAC;;;;;;sCAE3C,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,MAAM,CAAC,KAAK,EAAE,mBAAmB,QAAQ,CAAC;4BAAC;;;;;;;;;;;;;;;;;0BAM1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;4BACP,MAAM,QAAQ,SAAS,OAAO;4BAC9B,IAAI,OAAO;gCACT,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;4BACtD;wBACF;wBACA,WAAU;wBACV,UAAU,CAAC,OAAO;kCAElB,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAGf,8OAAC;wBACC,SAAS;wBACT,UAAU,CAAC,OAAO;wBAClB,WAAU;kCAET,0BACC,8OAAC;4BAAE,WAAU;;;;;mCACX,0BACF,8OAAC;4BAAE,WAAU;;;;;iDAEb,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAIjB,8OAAC;wBACC,SAAS;4BACP,MAAM,QAAQ,SAAS,OAAO;4BAC9B,IAAI,OAAO;gCACT,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,UAAU,MAAM,WAAW,GAAG;4BAC7D;wBACF;wBACA,WAAU;wBACV,UAAU,CAAC,OAAO;kCAElB,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;0BAKjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;;;;;;kCACb,8OAAC;wBACC,MAAK;wBACL,KAAI;wBACJ,KAAI;wBACJ,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,WAAU;;;;;;kCAEZ,8OAAC;wBAAE,WAAU;;;;;;kCACb,8OAAC;wBAAK,WAAU;;4BACb,KAAK,KAAK,CAAC,SAAS;4BAAK;;;;;;;;;;;;;;;;;;;AAKpC", "debugId": null}}, {"offset": {"line": 1685, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/nodeWork/music/music-generation-app/src/lib/musicApi.ts"], "sourcesContent": ["import axios from 'axios';\n\n// Gradio API 配置\nconst GRADIO_API_URL = 'http://************:7860';\n\n// 音乐风格选项\nexport const MUSIC_GENRES = [\n  'Pop',\n  'R&B', \n  'Dance',\n  'Jazz',\n  'Folk',\n  'Rock',\n  'Chinese Style',\n  'Chinese Tradition',\n  'Metal',\n  'Reggae',\n  'Chinese Opera',\n  'Auto'\n] as const;\n\nexport type MusicGenre = typeof MUSIC_GENRES[number];\n\n// 生成音乐的参数接口\nexport interface GenerateMusicParams {\n  lyric: string;\n  description?: string;\n  prompt_audio?: File;\n  genre: MusicGenre;\n  cfg_coef: number;\n  temperature: number;\n  top_k: number;\n}\n\n// API 响应接口\nexport interface GenerateMusicResponse {\n  audio_url: string;\n  generation_info: {\n    duration?: number;\n    sample_rate?: number;\n    channels?: number;\n    format?: string;\n    [key: string]: any;\n  };\n}\n\n// 错误处理接口\nexport interface ApiError {\n  message: string;\n  code?: string;\n  details?: any;\n}\n\nclass MusicApiService {\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = GRADIO_API_URL;\n  }\n\n  /**\n   * 生成音乐\n   */\n  async generateMusic(params: GenerateMusicParams): Promise<GenerateMusicResponse> {\n    try {\n      // 准备表单数据\n      const formData = new FormData();\n      \n      // 添加基本参数\n      formData.append('lyric', params.lyric);\n      formData.append('genre', params.genre);\n      formData.append('cfg_coef', params.cfg_coef.toString());\n      formData.append('temperature', params.temperature.toString());\n      formData.append('top_k', params.top_k.toString());\n      \n      // 添加可选参数\n      if (params.description) {\n        formData.append('description', params.description);\n      }\n      \n      if (params.prompt_audio) {\n        formData.append('prompt_audio', params.prompt_audio);\n      }\n\n      // 发送请求到 Gradio API\n      const response = await axios.post(\n        `${this.baseURL}/api/generate_song`,\n        formData,\n        {\n          headers: {\n            'Content-Type': 'multipart/form-data',\n          },\n          timeout: 300000, // 5分钟超时\n        }\n      );\n\n      // 处理响应\n      if (response.data && Array.isArray(response.data) && response.data.length >= 2) {\n        const [audioPath, generationInfo] = response.data;\n        \n        // 构建完整的音频URL\n        const audioUrl = audioPath.startsWith('http') \n          ? audioPath \n          : `${this.baseURL}/file=${audioPath}`;\n\n        return {\n          audio_url: audioUrl,\n          generation_info: generationInfo || {}\n        };\n      } else {\n        throw new Error('Invalid response format from API');\n      }\n    } catch (error) {\n      console.error('Music generation error:', error);\n      \n      if (axios.isAxiosError(error)) {\n        if (error.code === 'ECONNABORTED') {\n          throw new ApiError('请求超时，音乐生成可能需要更长时间，请稍后重试');\n        } else if (error.response) {\n          throw new ApiError(\n            `服务器错误: ${error.response.status} - ${error.response.statusText}`,\n            error.response.status.toString(),\n            error.response.data\n          );\n        } else if (error.request) {\n          throw new ApiError('无法连接到音乐生成服务，请检查网络连接');\n        }\n      }\n      \n      throw new ApiError(\n        error instanceof Error ? error.message : '音乐生成失败，请重试'\n      );\n    }\n  }\n\n  /**\n   * 检查服务状态\n   */\n  async checkServiceStatus(): Promise<boolean> {\n    try {\n      const response = await axios.get(`${this.baseURL}/`, {\n        timeout: 10000\n      });\n      return response.status === 200;\n    } catch (error) {\n      console.error('Service status check failed:', error);\n      return false;\n    }\n  }\n\n  /**\n   * 获取支持的音频格式\n   */\n  getSupportedAudioFormats(): string[] {\n    return [\n      'audio/mpeg',     // MP3\n      'audio/wav',      // WAV\n      'audio/flac',     // FLAC\n      'audio/aac',      // AAC\n      'audio/ogg',      // OGG\n      'audio/webm',     // WebM\n    ];\n  }\n\n  /**\n   * 验证音频文件\n   */\n  validateAudioFile(file: File): { valid: boolean; error?: string } {\n    const supportedFormats = this.getSupportedAudioFormats();\n    \n    if (!supportedFormats.includes(file.type)) {\n      return {\n        valid: false,\n        error: '不支持的音频格式，请使用 MP3、WAV、FLAC 或 AAC 格式'\n      };\n    }\n\n    // 检查文件大小 (最大 50MB)\n    const maxSize = 50 * 1024 * 1024;\n    if (file.size > maxSize) {\n      return {\n        valid: false,\n        error: '音频文件过大，请使用小于 50MB 的文件'\n      };\n    }\n\n    return { valid: true };\n  }\n\n  /**\n   * 获取默认生成参数\n   */\n  getDefaultParams(): Omit<GenerateMusicParams, 'lyric'> {\n    return {\n      genre: 'Pop',\n      cfg_coef: 1.5,\n      temperature: 0.9,\n      top_k: 50,\n    };\n  }\n}\n\n// 导出单例实例\nexport const musicApi = new MusicApiService();\n\n// 导出错误类\nexport class ApiError extends Error {\n  public code?: string;\n  public details?: any;\n\n  constructor(message: string, code?: string, details?: any) {\n    super(message);\n    this.name = 'ApiError';\n    this.code = code;\n    this.details = details;\n  }\n}\n\n// 工具函数：格式化文件大小\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n// 工具函数：格式化时长\nexport function formatDuration(seconds: number): string {\n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor((seconds % 3600) / 60);\n  const secs = Math.floor(seconds % 60);\n  \n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  } else {\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,gBAAgB;AAChB,MAAM,iBAAiB;AAGhB,MAAM,eAAe;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAkCD,MAAM;IACI,QAAgB;IAExB,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA;;GAEC,GACD,MAAM,cAAc,MAA2B,EAAkC;QAC/E,IAAI;YACF,SAAS;YACT,MAAM,WAAW,IAAI;YAErB,SAAS;YACT,SAAS,MAAM,CAAC,SAAS,OAAO,KAAK;YACrC,SAAS,MAAM,CAAC,SAAS,OAAO,KAAK;YACrC,SAAS,MAAM,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ;YACpD,SAAS,MAAM,CAAC,eAAe,OAAO,WAAW,CAAC,QAAQ;YAC1D,SAAS,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YAE9C,SAAS;YACT,IAAI,OAAO,WAAW,EAAE;gBACtB,SAAS,MAAM,CAAC,eAAe,OAAO,WAAW;YACnD;YAEA,IAAI,OAAO,YAAY,EAAE;gBACvB,SAAS,MAAM,CAAC,gBAAgB,OAAO,YAAY;YACrD;YAEA,mBAAmB;YACnB,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,EACnC,UACA;gBACE,SAAS;oBACP,gBAAgB;gBAClB;gBACA,SAAS;YACX;YAGF,OAAO;YACP,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC,MAAM,IAAI,GAAG;gBAC9E,MAAM,CAAC,WAAW,eAAe,GAAG,SAAS,IAAI;gBAEjD,aAAa;gBACb,MAAM,WAAW,UAAU,UAAU,CAAC,UAClC,YACA,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW;gBAEvC,OAAO;oBACL,WAAW;oBACX,iBAAiB,kBAAkB,CAAC;gBACtC;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,QAAQ;gBAC7B,IAAI,MAAM,IAAI,KAAK,gBAAgB;oBACjC,MAAM,IAAI,SAAS;gBACrB,OAAO,IAAI,MAAM,QAAQ,EAAE;oBACzB,MAAM,IAAI,SACR,CAAC,OAAO,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE,EAChE,MAAM,QAAQ,CAAC,MAAM,CAAC,QAAQ,IAC9B,MAAM,QAAQ,CAAC,IAAI;gBAEvB,OAAO,IAAI,MAAM,OAAO,EAAE;oBACxB,MAAM,IAAI,SAAS;gBACrB;YACF;YAEA,MAAM,IAAI,SACR,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE7C;IACF;IAEA;;GAEC,GACD,MAAM,qBAAuC;QAC3C,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBACnD,SAAS;YACX;YACA,OAAO,SAAS,MAAM,KAAK;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,2BAAqC;QACnC,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA;;GAEC,GACD,kBAAkB,IAAU,EAAsC;QAChE,MAAM,mBAAmB,IAAI,CAAC,wBAAwB;QAEtD,IAAI,CAAC,iBAAiB,QAAQ,CAAC,KAAK,IAAI,GAAG;YACzC,OAAO;gBACL,OAAO;gBACP,OAAO;YACT;QACF;QAEA,mBAAmB;QACnB,MAAM,UAAU,KAAK,OAAO;QAC5B,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO;gBACL,OAAO;gBACP,OAAO;YACT;QACF;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA;;GAEC,GACD,mBAAuD;QACrD,OAAO;YACL,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;QACT;IACF;AACF;AAGO,MAAM,WAAW,IAAI;AAGrB,MAAM,iBAAiB;IACrB,KAAc;IACd,QAAc;IAErB,YAAY,OAAe,EAAE,IAAa,EAAE,OAAa,CAAE;QACzD,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;IACjB;AACF;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;IAElC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC9F,OAAO;QACL,OAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACzD;AACF", "debugId": null}}, {"offset": {"line": 1864, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/nodeWork/music/music-generation-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport RichTextEditor from '@/components/RichTextEditor';\nimport AudioUpload from '@/components/AudioUpload';\nimport SongDescriptionSelector from '@/components/SongDescriptionSelector';\nimport AudioPlayer from '@/components/AudioPlayer';\nimport { musicApi, MUSIC_GENRES, type MusicGenre, type GenerateMusicParams, ApiError } from '@/lib/musicApi';\n\nexport default function Home() {\n  // 表单状态\n  const [lyrics, setLyrics] = useState('');\n  const [description, setDescription] = useState('');\n  const [audioFile, setAudioFile] = useState<File | null>(null);\n  const [selectedGenre, setSelectedGenre] = useState<MusicGenre>('Pop');\n  const [advancedSettings, setAdvancedSettings] = useState({\n    cfgCoef: 1.5,\n    temperature: 0.9,\n    topK: 50,\n  });\n  const [showAdvanced, setShowAdvanced] = useState(false);\n\n  // 生成状态\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generatedAudio, setGeneratedAudio] = useState<string>('');\n  const [generationInfo, setGenerationInfo] = useState<any>(null);\n  const [error, setError] = useState<string>('');\n\n  // 处理音乐生成\n  const handleGenerate = async () => {\n    if (!lyrics.trim()) {\n      setError('请输入歌词内容');\n      return;\n    }\n\n    setIsGenerating(true);\n    setError('');\n    setGeneratedAudio('');\n\n    try {\n      const params: GenerateMusicParams = {\n        lyric: lyrics,\n        description: description || undefined,\n        prompt_audio: audioFile || undefined,\n        genre: selectedGenre,\n        cfg_coef: advancedSettings.cfgCoef,\n        temperature: advancedSettings.temperature,\n        top_k: advancedSettings.topK,\n      };\n\n      const result = await musicApi.generateMusic(params);\n      setGeneratedAudio(result.audio_url);\n      setGenerationInfo(result.generation_info);\n    } catch (err) {\n      if (err instanceof ApiError) {\n        setError(err.message);\n      } else {\n        setError('音乐生成失败，请重试');\n      }\n      console.error('Generation error:', err);\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  // 重置表单\n  const handleReset = () => {\n    setLyrics('');\n    setDescription('');\n    setAudioFile(null);\n    setSelectedGenre('Pop');\n    setAdvancedSettings({\n      cfgCoef: 1.5,\n      temperature: 0.9,\n      topK: 50,\n    });\n    setGeneratedAudio('');\n    setGenerationInfo(null);\n    setError('');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* 头部 */}\n      <header className=\"border-b border-muted/20 bg-background/80 backdrop-blur-sm sticky top-0 z-50\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-primary rounded-lg\">\n                <i className=\"fas fa-music text-white text-lg\"></i>\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent\">\n                  AI音乐创作平台\n                </h1>\n                <p className=\"text-sm text-muted-foreground\">基于人工智能的音乐生成服务</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={handleReset}\n                className=\"px-4 py-2 text-sm bg-muted/20 hover:bg-muted/40 rounded-lg transition-colors\"\n              >\n                <i className=\"fas fa-refresh mr-2\"></i>\n                重置\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* 左侧：输入区域 */}\n          <div className=\"space-y-6\">\n            {/* 歌词输入 */}\n            <div className=\"card p-6\">\n              <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n                <i className=\"fas fa-pen-fancy mr-3 text-primary\"></i>\n                歌词创作\n              </h2>\n              <RichTextEditor\n                value={lyrics}\n                onChange={setLyrics}\n                placeholder=\"请输入您的歌词内容...\"\n                maxLength={2000}\n              />\n            </div>\n\n            {/* 选项卡 */}\n            <div className=\"card\">\n              <div className=\"border-b border-muted/20\">\n                <nav className=\"flex space-x-8 px-6\">\n                  <button className=\"py-4 border-b-2 border-primary text-primary font-medium\">\n                    风格选择\n                  </button>\n                  <button className=\"py-4 border-b-2 border-transparent text-muted-foreground hover:text-foreground font-medium\">\n                    音频提示\n                  </button>\n                  <button className=\"py-4 border-b-2 border-transparent text-muted-foreground hover:text-foreground font-medium\">\n                    文本提示\n                  </button>\n                </nav>\n              </div>\n\n              <div className=\"p-6\">\n                <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n                  <i className=\"fas fa-palette mr-3 text-primary\"></i>\n                  音乐风格\n                </h3>\n                <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n                  {MUSIC_GENRES.map((genre) => (\n                    <button\n                      key={genre}\n                      onClick={() => setSelectedGenre(genre)}\n                      className={`p-3 rounded-lg border transition-all ${\n                        selectedGenre === genre\n                          ? 'border-primary bg-primary/10 text-primary'\n                          : 'border-muted/30 hover:border-primary/50 hover:bg-primary/5'\n                      }`}\n                    >\n                      {genre === 'Chinese Style' ? '中国风' :\n                       genre === 'Chinese Tradition' ? '传统' :\n                       genre === 'Chinese Opera' ? '戏曲' :\n                       genre === 'R&B' ? '节奏布鲁斯' :\n                       genre === 'Pop' ? '流行' :\n                       genre === 'Dance' ? '舞曲' :\n                       genre === 'Jazz' ? '爵士' :\n                       genre === 'Folk' ? '民谣' :\n                       genre === 'Rock' ? '摇滚' :\n                       genre === 'Metal' ? '金属' :\n                       genre === 'Reggae' ? '雷鬼' :\n                       genre === 'Auto' ? '自动' : genre}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* 音频上传 */}\n            <div className=\"card p-6\">\n              <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n                <i className=\"fas fa-upload mr-3 text-primary\"></i>\n                音频提示 (可选)\n              </h2>\n              <AudioUpload\n                onFileSelect={setAudioFile}\n                maxSize={50}\n              />\n            </div>\n\n            {/* 歌曲描述 */}\n            <div className=\"card p-6\">\n              <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n                <i className=\"fas fa-edit mr-3 text-primary\"></i>\n                歌曲描述 (可选)\n              </h2>\n              <SongDescriptionSelector\n                value={description}\n                onChange={setDescription}\n              />\n            </div>\n\n            {/* 高级设置 */}\n            <div className=\"card p-6\">\n              <button\n                onClick={() => setShowAdvanced(!showAdvanced)}\n                className=\"w-full flex items-center justify-between text-lg font-semibold mb-4\"\n              >\n                <div className=\"flex items-center\">\n                  <i className=\"fas fa-cog mr-3 text-primary\"></i>\n                  高级设置\n                </div>\n                <i className={`fas fa-chevron-${showAdvanced ? 'up' : 'down'} text-muted-foreground`}></i>\n              </button>\n\n              {showAdvanced && (\n                <div className=\"space-y-4 pt-4 border-t border-muted/20\">\n                  <div>\n                    <label className=\"block text-sm font-medium mb-2\">\n                      CFG系数: {advancedSettings.cfgCoef}\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"1\"\n                      max=\"3\"\n                      step=\"0.1\"\n                      value={advancedSettings.cfgCoef}\n                      onChange={(e) => setAdvancedSettings(prev => ({\n                        ...prev,\n                        cfgCoef: parseFloat(e.target.value)\n                      }))}\n                      className=\"w-full h-2 bg-muted/30 rounded-lg appearance-none cursor-pointer\"\n                    />\n                    <div className=\"text-xs text-muted-foreground mt-1\">\n                      控制生成结果与提示的匹配程度\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium mb-2\">\n                      温度: {advancedSettings.temperature}\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"0.1\"\n                      max=\"1.5\"\n                      step=\"0.1\"\n                      value={advancedSettings.temperature}\n                      onChange={(e) => setAdvancedSettings(prev => ({\n                        ...prev,\n                        temperature: parseFloat(e.target.value)\n                      }))}\n                      className=\"w-full h-2 bg-muted/30 rounded-lg appearance-none cursor-pointer\"\n                    />\n                    <div className=\"text-xs text-muted-foreground mt-1\">\n                      控制生成结果的随机性和创造性\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium mb-2\">\n                      Top-K: {advancedSettings.topK}\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"10\"\n                      max=\"100\"\n                      step=\"10\"\n                      value={advancedSettings.topK}\n                      onChange={(e) => setAdvancedSettings(prev => ({\n                        ...prev,\n                        topK: parseInt(e.target.value)\n                      }))}\n                      className=\"w-full h-2 bg-muted/30 rounded-lg appearance-none cursor-pointer\"\n                    />\n                    <div className=\"text-xs text-muted-foreground mt-1\">\n                      限制候选词汇的数量，影响生成质量\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 右侧：播放器和控制 */}\n          <div className=\"space-y-6\">\n            {/* 生成按钮 */}\n            <div className=\"card p-6\">\n              <button\n                onClick={handleGenerate}\n                disabled={isGenerating || !lyrics.trim()}\n                className=\"w-full btn-primary py-4 px-6 rounded-lg font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3\"\n              >\n                {isGenerating ? (\n                  <>\n                    <i className=\"fas fa-spinner fa-spin\"></i>\n                    <span>正在生成音乐...</span>\n                  </>\n                ) : (\n                  <>\n                    <i className=\"fas fa-magic\"></i>\n                    <span>生成歌曲</span>\n                  </>\n                )}\n              </button>\n\n              {error && (\n                <div className=\"mt-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-600\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-exclamation-circle mr-2\"></i>\n                    <span className=\"font-medium\">生成失败</span>\n                  </div>\n                  <p className=\"text-sm mt-1\">{error}</p>\n                </div>\n              )}\n\n              {isGenerating && (\n                <div className=\"mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg text-blue-600\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-info-circle mr-2\"></i>\n                    <span className=\"font-medium\">生成提示</span>\n                  </div>\n                  <p className=\"text-sm mt-1\">\n                    音乐生成通常需要1-3分钟，请耐心等待...\n                  </p>\n                </div>\n              )}\n            </div>\n\n            {/* 音频播放器 */}\n            <div className=\"card\">\n              <h2 className=\"text-xl font-semibold p-6 pb-0 flex items-center\">\n                <i className=\"fas fa-play-circle mr-3 text-primary\"></i>\n                音频播放器\n              </h2>\n              <AudioPlayer\n                src={generatedAudio}\n                title=\"生成的音乐\"\n                artist=\"AI音乐创作\"\n              />\n            </div>\n\n            {/* 生成信息 */}\n            {generationInfo && (\n              <div className=\"card p-6\">\n                <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n                  <i className=\"fas fa-info-circle mr-3 text-primary\"></i>\n                  生成信息\n                </h3>\n                <div className=\"space-y-2 text-sm\">\n                  {generationInfo.duration && (\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-muted-foreground\">时长:</span>\n                      <span>{Math.round(generationInfo.duration)}秒</span>\n                    </div>\n                  )}\n                  {generationInfo.sample_rate && (\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-muted-foreground\">采样率:</span>\n                      <span>{generationInfo.sample_rate}Hz</span>\n                    </div>\n                  )}\n                  {generationInfo.format && (\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-muted-foreground\">格式:</span>\n                      <span>{generationInfo.format}</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* 使用说明 */}\n            <div className=\"card p-6\">\n              <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n                <i className=\"fas fa-question-circle mr-3 text-primary\"></i>\n                使用说明\n              </h3>\n              <div className=\"space-y-3 text-sm text-muted-foreground\">\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary mt-0.5\">1</span>\n                  <span>在左侧输入您的歌词内容，支持结构化标签如 [verse], [chorus] 等</span>\n                </div>\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary mt-0.5\">2</span>\n                  <span>选择音乐风格，可以上传参考音频或添加文本描述</span>\n                </div>\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary mt-0.5\">3</span>\n                  <span>点击\"生成歌曲\"按钮，等待AI为您创作音乐</span>\n                </div>\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary mt-0.5\">4</span>\n                  <span>生成完成后，您可以在音频播放器中试听和下载</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,OAAO;IACP,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,SAAS;QACT,aAAa;QACb,MAAM;IACR;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,OAAO;IACP,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,SAAS;IACT,MAAM,iBAAiB;QACrB,IAAI,CAAC,OAAO,IAAI,IAAI;YAClB,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QACT,kBAAkB;QAElB,IAAI;YACF,MAAM,SAA8B;gBAClC,OAAO;gBACP,aAAa,eAAe;gBAC5B,cAAc,aAAa;gBAC3B,OAAO;gBACP,UAAU,iBAAiB,OAAO;gBAClC,aAAa,iBAAiB,WAAW;gBACzC,OAAO,iBAAiB,IAAI;YAC9B;YAEA,MAAM,SAAS,MAAM,sHAAA,CAAA,WAAQ,CAAC,aAAa,CAAC;YAC5C,kBAAkB,OAAO,SAAS;YAClC,kBAAkB,OAAO,eAAe;QAC1C,EAAE,OAAO,KAAK;YACZ,IAAI,eAAe,sHAAA,CAAA,WAAQ,EAAE;gBAC3B,SAAS,IAAI,OAAO;YACtB,OAAO;gBACL,SAAS;YACX;YACA,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,OAAO;IACP,MAAM,cAAc;QAClB,UAAU;QACV,eAAe;QACf,aAAa;QACb,iBAAiB;QACjB,oBAAoB;YAClB,SAAS;YACT,aAAa;YACb,MAAM;QACR;QACA,kBAAkB;QAClB,kBAAkB;QAClB,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA8F;;;;;;0DAG5G,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAGjD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC;4CAAE,WAAU;;;;;;wCAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjD,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAE,WAAU;;;;;;gDAAyC;;;;;;;sDAGxD,8OAAC,oIAAA,CAAA,UAAc;4CACb,OAAO;4CACP,UAAU;4CACV,aAAY;4CACZ,WAAW;;;;;;;;;;;;8CAKf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAO,WAAU;kEAA0D;;;;;;kEAG5E,8OAAC;wDAAO,WAAU;kEAA6F;;;;;;kEAG/G,8OAAC;wDAAO,WAAU;kEAA6F;;;;;;;;;;;;;;;;;sDAMnH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAE,WAAU;;;;;;wDAAuC;;;;;;;8DAGtD,8OAAC;oDAAI,WAAU;8DACZ,sHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,sBACjB,8OAAC;4DAEC,SAAS,IAAM,iBAAiB;4DAChC,WAAW,CAAC,qCAAqC,EAC/C,kBAAkB,QACd,8CACA,8DACJ;sEAED,UAAU,kBAAkB,QAC5B,UAAU,sBAAsB,OAChC,UAAU,kBAAkB,OAC5B,UAAU,QAAQ,UAClB,UAAU,QAAQ,OAClB,UAAU,UAAU,OACpB,UAAU,SAAS,OACnB,UAAU,SAAS,OACnB,UAAU,SAAS,OACnB,UAAU,UAAU,OACpB,UAAU,WAAW,OACrB,UAAU,SAAS,OAAO;2DAnBtB;;;;;;;;;;;;;;;;;;;;;;8CA2Bf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAE,WAAU;;;;;;gDAAsC;;;;;;;sDAGrD,8OAAC,iIAAA,CAAA,UAAW;4CACV,cAAc;4CACd,SAAS;;;;;;;;;;;;8CAKb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAE,WAAU;;;;;;gDAAoC;;;;;;;sDAGnD,8OAAC,6IAAA,CAAA,UAAuB;4CACtB,OAAO;4CACP,UAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,gBAAgB,CAAC;4CAChC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;wDAAmC;;;;;;;8DAGlD,8OAAC;oDAAE,WAAW,CAAC,eAAe,EAAE,eAAe,OAAO,OAAO,sBAAsB,CAAC;;;;;;;;;;;;wCAGrF,8BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;;gEAAiC;gEACxC,iBAAiB,OAAO;;;;;;;sEAElC,8OAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,MAAK;4DACL,OAAO,iBAAiB,OAAO;4DAC/B,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wEAC5C,GAAG,IAAI;wEACP,SAAS,WAAW,EAAE,MAAM,CAAC,KAAK;oEACpC,CAAC;4DACD,WAAU;;;;;;sEAEZ,8OAAC;4DAAI,WAAU;sEAAqC;;;;;;;;;;;;8DAKtD,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;;gEAAiC;gEAC3C,iBAAiB,WAAW;;;;;;;sEAEnC,8OAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,MAAK;4DACL,OAAO,iBAAiB,WAAW;4DACnC,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wEAC5C,GAAG,IAAI;wEACP,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;oEACxC,CAAC;4DACD,WAAU;;;;;;sEAEZ,8OAAC;4DAAI,WAAU;sEAAqC;;;;;;;;;;;;8DAKtD,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;;gEAAiC;gEACxC,iBAAiB,IAAI;;;;;;;sEAE/B,8OAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,MAAK;4DACL,OAAO,iBAAiB,IAAI;4DAC5B,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wEAC5C,GAAG,IAAI;wEACP,MAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oEAC/B,CAAC;4DACD,WAAU;;;;;;sEAEZ,8OAAC;4DAAI,WAAU;sEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAU9D,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,UAAU,gBAAgB,CAAC,OAAO,IAAI;4CACtC,WAAU;sDAET,6BACC;;kEACE,8OAAC;wDAAE,WAAU;;;;;;kEACb,8OAAC;kEAAK;;;;;;;6EAGR;;kEACE,8OAAC;wDAAE,WAAU;;;;;;kEACb,8OAAC;kEAAK;;;;;;;;;;;;;wCAKX,uBACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAEhC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;wCAIhC,8BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAEhC,8OAAC;oDAAE,WAAU;8DAAe;;;;;;;;;;;;;;;;;;8CAQlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAE,WAAU;;;;;;gDAA2C;;;;;;;sDAG1D,8OAAC,iIAAA,CAAA,UAAW;4CACV,KAAK;4CACL,OAAM;4CACN,QAAO;;;;;;;;;;;;gCAKV,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAE,WAAU;;;;;;gDAA2C;;;;;;;sDAG1D,8OAAC;4CAAI,WAAU;;gDACZ,eAAe,QAAQ,kBACtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;;gEAAM,KAAK,KAAK,CAAC,eAAe,QAAQ;gEAAE;;;;;;;;;;;;;gDAG9C,eAAe,WAAW,kBACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;;gEAAM,eAAe,WAAW;gEAAC;;;;;;;;;;;;;gDAGrC,eAAe,MAAM,kBACpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;sEAAM,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;;8CAQtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAE,WAAU;;;;;;gDAA+C;;;;;;;sDAG9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA0H;;;;;;sEAC1I,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA0H;;;;;;sEAC1I,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA0H;;;;;;sEAC1I,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA0H;;;;;;sEAC1I,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}]}