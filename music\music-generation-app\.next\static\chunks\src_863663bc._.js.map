{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/nodeWork/music/music-generation-app/src/components/RichTextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEditor, EditorContent } from '@tiptap/react';\nimport StarterKit from '@tiptap/starter-kit';\nimport Placeholder from '@tiptap/extension-placeholder';\nimport CharacterCount from '@tiptap/extension-character-count';\nimport { useEffect } from 'react';\n\ninterface RichTextEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  maxLength?: number;\n  className?: string;\n}\n\nexport default function RichTextEditor({\n  value,\n  onChange,\n  placeholder = \"请输入歌词内容...\",\n  maxLength = 2000,\n  className = \"\"\n}: RichTextEditorProps) {\n  const editor = useEditor({\n    extensions: [\n      StarterKit,\n      Placeholder.configure({\n        placeholder,\n      }),\n      CharacterCount.configure({\n        limit: maxLength,\n      }),\n    ],\n    content: value,\n    onUpdate: ({ editor }) => {\n      // 使用 getText() 获取纯文本而不是 HTML\n      onChange(editor.getText());\n    },\n    editorProps: {\n      attributes: {\n        class: 'prose prose-invert max-w-none focus:outline-none',\n      },\n    },\n  });\n\n  useEffect(() => {\n    if (editor && editor.getText() !== value) {\n      editor.commands.setContent(value);\n    }\n  }, [value, editor]);\n\n  if (!editor) {\n    return null;\n  }\n\n  const characterCount = editor.storage.characterCount.characters();\n  const wordCount = editor.storage.characterCount.words();\n\n  return (\n    <div className={`space-y-3 ${className}`}>\n      {/* 工具栏 */}\n      <div className=\"flex flex-wrap gap-2 p-3 bg-muted/20 rounded-lg border border-muted/30\">\n        <button\n          onClick={() => editor.chain().focus().toggleBold().run()}\n          className={`px-3 py-1 rounded text-sm transition-colors ${\n            editor.isActive('bold')\n              ? 'bg-primary text-white'\n              : 'bg-muted/30 hover:bg-muted/50'\n          }`}\n          type=\"button\"\n        >\n          <i className=\"fas fa-bold\"></i>\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleItalic().run()}\n          className={`px-3 py-1 rounded text-sm transition-colors ${\n            editor.isActive('italic')\n              ? 'bg-primary text-white'\n              : 'bg-muted/30 hover:bg-muted/50'\n          }`}\n          type=\"button\"\n        >\n          <i className=\"fas fa-italic\"></i>\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleStrike().run()}\n          className={`px-3 py-1 rounded text-sm transition-colors ${\n            editor.isActive('strike')\n              ? 'bg-primary text-white'\n              : 'bg-muted/30 hover:bg-muted/50'\n          }`}\n          type=\"button\"\n        >\n          <i className=\"fas fa-strikethrough\"></i>\n        </button>\n        <div className=\"w-px bg-muted/30 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().toggleBulletList().run()}\n          className={`px-3 py-1 rounded text-sm transition-colors ${\n            editor.isActive('bulletList')\n              ? 'bg-primary text-white'\n              : 'bg-muted/30 hover:bg-muted/50'\n          }`}\n          type=\"button\"\n        >\n          <i className=\"fas fa-list-ul\"></i>\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleOrderedList().run()}\n          className={`px-3 py-1 rounded text-sm transition-colors ${\n            editor.isActive('orderedList')\n              ? 'bg-primary text-white'\n              : 'bg-muted/30 hover:bg-muted/50'\n          }`}\n          type=\"button\"\n        >\n          <i className=\"fas fa-list-ol\"></i>\n        </button>\n        <div className=\"w-px bg-muted/30 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().undo().run()}\n          disabled={!editor.can().undo()}\n          className=\"px-3 py-1 rounded text-sm bg-muted/30 hover:bg-muted/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          type=\"button\"\n        >\n          <i className=\"fas fa-undo\"></i>\n        </button>\n        <button\n          onClick={() => editor.chain().focus().redo().run()}\n          disabled={!editor.can().redo()}\n          className=\"px-3 py-1 rounded text-sm bg-muted/30 hover:bg-muted/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          type=\"button\"\n        >\n          <i className=\"fas fa-redo\"></i>\n        </button>\n      </div>\n\n      {/* 编辑器内容 */}\n      <div className=\"relative\">\n        <EditorContent editor={editor} className=\"min-h-[200px]\" />\n      </div>\n\n      {/* 字符统计 */}\n      <div className=\"flex justify-between items-center text-sm text-muted-foreground\">\n        <div className=\"flex gap-4\">\n          <span>字符: {characterCount}/{maxLength}</span>\n          <span>词数: {wordCount}</span>\n        </div>\n        {characterCount > maxLength * 0.9 && (\n          <div className=\"text-yellow-500\">\n            <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n            接近字符限制\n          </div>\n        )}\n      </div>\n\n      {/* 歌词结构提示 */}\n      <div className=\"p-3 bg-accent/10 rounded-lg border border-accent/20\">\n        <div className=\"text-sm text-accent-foreground\">\n          <i className=\"fas fa-info-circle mr-2\"></i>\n          <strong>歌词结构提示：</strong>\n        </div>\n        <div className=\"text-xs text-muted-foreground mt-1 space-y-1\">\n          <div>• 每段代表一个分段，以结构标签开头，以空行结尾</div>\n          <div>• 每行代表一个句子，不建议使用标点符号</div>\n          <div>• 分段标签：[intro], [verse], [chorus], [bridge], [outro]</div>\n          <div>• [intro], [inst], [outro] 不应包含歌词</div>\n          <div>• [verse], [chorus], [bridge] 需要歌词内容</div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAgBe,SAAS,eAAe,EACrC,KAAK,EACL,QAAQ,EACR,cAAc,YAAY,EAC1B,YAAY,IAAI,EAChB,YAAY,EAAE,EACM;;IACpB,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE;QACvB,YAAY;YACV,8JAAA,CAAA,UAAU;YACV,wKAAA,CAAA,UAAW,CAAC,SAAS,CAAC;gBACpB;YACF;YACA,+KAAA,CAAA,UAAc,CAAC,SAAS,CAAC;gBACvB,OAAO;YACT;SACD;QACD,SAAS;QACT,QAAQ;gDAAE,CAAC,EAAE,MAAM,EAAE;gBACnB,6BAA6B;gBAC7B,SAAS,OAAO,OAAO;YACzB;;QACA,aAAa;YACX,YAAY;gBACV,OAAO;YACT;QACF;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,UAAU,OAAO,OAAO,OAAO,OAAO;gBACxC,OAAO,QAAQ,CAAC,UAAU,CAAC;YAC7B;QACF;mCAAG;QAAC;QAAO;KAAO;IAElB,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,MAAM,iBAAiB,OAAO,OAAO,CAAC,cAAc,CAAC,UAAU;IAC/D,MAAM,YAAY,OAAO,OAAO,CAAC,cAAc,CAAC,KAAK;IAErD,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;wBACtD,WAAW,CAAC,4CAA4C,EACtD,OAAO,QAAQ,CAAC,UACZ,0BACA,iCACJ;wBACF,MAAK;kCAEL,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,WAAW,CAAC,4CAA4C,EACtD,OAAO,QAAQ,CAAC,YACZ,0BACA,iCACJ;wBACF,MAAK;kCAEL,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,WAAW,CAAC,4CAA4C,EACtD,OAAO,QAAQ,CAAC,YACZ,0BACA,iCACJ;wBACF,MAAK;kCAEL,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAW,CAAC,4CAA4C,EACtD,OAAO,QAAQ,CAAC,gBACZ,0BACA,iCACJ;wBACF,MAAK;kCAEL,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,iBAAiB,GAAG,GAAG;wBAC7D,WAAW,CAAC,4CAA4C,EACtD,OAAO,QAAQ,CAAC,iBACZ,0BACA,iCACJ;wBACF,MAAK;kCAEL,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAChD,UAAU,CAAC,OAAO,GAAG,GAAG,IAAI;wBAC5B,WAAU;wBACV,MAAK;kCAEL,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;wBAChD,UAAU,CAAC,OAAO,GAAG,GAAG,IAAI;wBAC5B,WAAU;wBACV,MAAK;kCAEL,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;0BAKjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qKAAA,CAAA,gBAAa;oBAAC,QAAQ;oBAAQ,WAAU;;;;;;;;;;;0BAI3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAK;oCAAK;oCAAe;oCAAE;;;;;;;0CAC5B,6LAAC;;oCAAK;oCAAK;;;;;;;;;;;;;oBAEZ,iBAAiB,YAAY,qBAC5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;4BAAuC;;;;;;;;;;;;;0BAO1D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;0CAAO;;;;;;;;;;;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAI;;;;;;0CACL,6LAAC;0CAAI;;;;;;0CACL,6LAAC;0CAAI;;;;;;0CACL,6LAAC;0CAAI;;;;;;0CACL,6LAAC;0CAAI;;;;;;;;;;;;;;;;;;;;;;;;AAKf;GA5JwB;;QAOP,qKAAA,CAAA,YAAS;;;KAPF", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/nodeWork/music/music-generation-app/src/components/AudioUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useCallback } from 'react';\n\ninterface AudioUploadProps {\n  onFileSelect: (file: File | null) => void;\n  accept?: string;\n  maxSize?: number; // in MB\n  className?: string;\n}\n\nexport default function AudioUpload({\n  onFileSelect,\n  accept = \"audio/*\",\n  maxSize = 10,\n  className = \"\"\n}: AudioUploadProps) {\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [error, setError] = useState<string>(\"\");\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const validateFile = (file: File): string | null => {\n    // 检查文件类型\n    if (!file.type.startsWith('audio/')) {\n      return '请选择音频文件';\n    }\n\n    // 检查文件大小\n    const fileSizeMB = file.size / (1024 * 1024);\n    if (fileSizeMB > maxSize) {\n      return `文件大小不能超过 ${maxSize}MB`;\n    }\n\n    return null;\n  };\n\n  const handleFileSelect = useCallback((file: File) => {\n    const validationError = validateFile(file);\n    if (validationError) {\n      setError(validationError);\n      setSelectedFile(null);\n      onFileSelect(null);\n      return;\n    }\n\n    setError(\"\");\n    setSelectedFile(file);\n    onFileSelect(file);\n  }, [maxSize, onFileSelect]);\n\n  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      handleFileSelect(file);\n    }\n  };\n\n  const handleDragOver = (event: React.DragEvent) => {\n    event.preventDefault();\n    setIsDragOver(true);\n  };\n\n  const handleDragLeave = (event: React.DragEvent) => {\n    event.preventDefault();\n    setIsDragOver(false);\n  };\n\n  const handleDrop = (event: React.DragEvent) => {\n    event.preventDefault();\n    setIsDragOver(false);\n\n    const files = event.dataTransfer.files;\n    if (files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  };\n\n  const handleRemoveFile = () => {\n    setSelectedFile(null);\n    setError(\"\");\n    onFileSelect(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = \"\";\n    }\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const formatDuration = (duration: number): string => {\n    const minutes = Math.floor(duration / 60);\n    const seconds = Math.floor(duration % 60);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* 上传区域 */}\n      <div\n        className={`\n          relative border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200\n          ${isDragOver \n            ? 'border-primary bg-primary/5' \n            : selectedFile \n              ? 'border-green-500 bg-green-500/5' \n              : 'border-muted hover:border-primary/50 hover:bg-primary/5'\n          }\n          ${error ? 'border-red-500 bg-red-500/5' : ''}\n        `}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          accept={accept}\n          onChange={handleFileInputChange}\n          className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n        />\n\n        {selectedFile ? (\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-green-500/20 rounded-full\">\n              <i className=\"fas fa-music text-2xl text-green-500\"></i>\n            </div>\n            <div>\n              <p className=\"font-medium text-green-600\">{selectedFile.name}</p>\n              <p className=\"text-sm text-muted-foreground\">\n                {formatFileSize(selectedFile.size)}\n              </p>\n            </div>\n            <button\n              onClick={handleRemoveFile}\n              className=\"inline-flex items-center px-3 py-1 text-sm bg-red-500/20 text-red-600 rounded-md hover:bg-red-500/30 transition-colors\"\n            >\n              <i className=\"fas fa-trash mr-1\"></i>\n              移除文件\n            </button>\n          </div>\n        ) : (\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-muted/20 rounded-full\">\n              <i className={`fas fa-cloud-upload-alt text-2xl ${isDragOver ? 'text-primary' : 'text-muted-foreground'}`}></i>\n            </div>\n            <div>\n              <p className=\"text-lg font-medium\">\n                {isDragOver ? '释放文件以上传' : '拖拽音频文件到此处'}\n              </p>\n              <p className=\"text-sm text-muted-foreground\">\n                或 <span className=\"text-primary cursor-pointer hover:underline\">点击选择文件</span>\n              </p>\n            </div>\n            <div className=\"text-xs text-muted-foreground\">\n              支持格式：MP3, WAV, FLAC, AAC 等 • 最大 {maxSize}MB\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 错误信息 */}\n      {error && (\n        <div className=\"flex items-center p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-600\">\n          <i className=\"fas fa-exclamation-circle mr-2\"></i>\n          {error}\n        </div>\n      )}\n\n      {/* 上传提示 */}\n      <div className=\"p-3 bg-accent/10 rounded-lg border border-accent/20\">\n        <div className=\"text-sm text-accent-foreground\">\n          <i className=\"fas fa-info-circle mr-2\"></i>\n          <strong>音频提示说明：</strong>\n        </div>\n        <div className=\"text-xs text-muted-foreground mt-1 space-y-1\">\n          <div>• 上传的音频文件将作为生成音乐的参考</div>\n          <div>• 系统会分析音频的风格、节奏和音色特征</div>\n          <div>• 建议上传高质量的音频文件以获得更好的效果</div>\n          <div>• 音频时长建议在30秒到5分钟之间</div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAWe,SAAS,YAAY,EAClC,YAAY,EACZ,SAAS,SAAS,EAClB,UAAU,EAAE,EACZ,YAAY,EAAE,EACG;;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,eAAe,CAAC;QACpB,SAAS;QACT,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,OAAO;QACT;QAEA,SAAS;QACT,MAAM,aAAa,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;QAC3C,IAAI,aAAa,SAAS;YACxB,OAAO,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC;QAChC;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACpC,MAAM,kBAAkB,aAAa;YACrC,IAAI,iBAAiB;gBACnB,SAAS;gBACT,gBAAgB;gBAChB,aAAa;gBACb;YACF;YAEA,SAAS;YACT,gBAAgB;YAChB,aAAa;QACf;oDAAG;QAAC;QAAS;KAAa;IAE1B,MAAM,wBAAwB,CAAC;QAC7B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc;QACpB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,cAAc;QACpB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;QACpB,cAAc;QAEd,MAAM,QAAQ,MAAM,YAAY,CAAC,KAAK;QACtC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,SAAS;QACT,aAAa;QACb,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,KAAK,KAAK,CAAC,WAAW;QACtC,MAAM,UAAU,KAAK,KAAK,CAAC,WAAW;QACtC,OAAO,GAAG,QAAQ,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC5D;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6LAAC;gBACC,WAAW,CAAC;;UAEV,EAAE,aACE,gCACA,eACE,oCACA,0DACL;UACD,EAAE,QAAQ,gCAAgC,GAAG;QAC/C,CAAC;gBACD,YAAY;gBACZ,aAAa;gBACb,QAAQ;;kCAER,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,UAAU;wBACV,WAAU;;;;;;oBAGX,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAA8B,aAAa,IAAI;;;;;;kDAC5D,6LAAC;wCAAE,WAAU;kDACV,eAAe,aAAa,IAAI;;;;;;;;;;;;0CAGrC,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC;wCAAE,WAAU;;;;;;oCAAwB;;;;;;;;;;;;6CAKzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAW,CAAC,iCAAiC,EAAE,aAAa,iBAAiB,yBAAyB;;;;;;;;;;;0CAE3G,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDACV,aAAa,YAAY;;;;;;kDAE5B,6LAAC;wCAAE,WAAU;;4CAAgC;0DACzC,6LAAC;gDAAK,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;0CAGpE,6LAAC;gCAAI,WAAU;;oCAAgC;oCACZ;oCAAQ;;;;;;;;;;;;;;;;;;;YAOhD,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;0BAKL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;0CAAO;;;;;;;;;;;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAI;;;;;;0CACL,6LAAC;0CAAI;;;;;;0CACL,6LAAC;0CAAI;;;;;;0CACL,6LAAC;0CAAI;;;;;;;;;;;;;;;;;;;;;;;;AAKf;GAlLwB;KAAA", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/nodeWork/music/music-generation-app/src/components/SongDescriptionSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SongDescriptionSelectorProps {\n  value: string;\n  onChange: (value: string) => void;\n  className?: string;\n}\n\n// 预设的歌曲描述选项\nconst PRESET_DESCRIPTIONS = {\n  gender: {\n    title: '性别',\n    icon: 'fas fa-user',\n    options: [\n      { label: '男声', value: 'male vocals', description: '男性歌手演唱' },\n      { label: '女声', value: 'female vocals', description: '女性歌手演唱' },\n      { label: '混合', value: 'mixed vocals', description: '男女混合演唱' },\n      { label: '童声', value: 'children vocals', description: '儿童歌手演唱' },\n    ]\n  },\n  mood: {\n    title: '情感',\n    icon: 'fas fa-heart',\n    options: [\n      { label: '快乐', value: 'happy, upbeat, joyful', description: '欢快愉悦的情感' },\n      { label: '悲伤', value: 'sad, melancholic, emotional', description: '悲伤忧郁的情感' },\n      { label: '浪漫', value: 'romantic, love, tender', description: '浪漫温柔的情感' },\n      { label: '激昂', value: 'energetic, powerful, passionate', description: '激情澎湃的情感' },\n      { label: '平静', value: 'calm, peaceful, serene', description: '平静安详的情感' },\n      { label: '神秘', value: 'mysterious, dark, atmospheric', description: '神秘深邃的情感' },\n    ]\n  },\n  style: {\n    title: '风格',\n    icon: 'fas fa-music',\n    options: [\n      { label: '流行', value: 'pop, mainstream, catchy', description: '流行音乐风格' },\n      { label: '摇滚', value: 'rock, electric guitar, drums', description: '摇滚音乐风格' },\n      { label: '民谣', value: 'folk, acoustic, storytelling', description: '民谣音乐风格' },\n      { label: '电子', value: 'electronic, synthesizer, digital', description: '电子音乐风格' },\n      { label: '爵士', value: 'jazz, swing, improvisation', description: '爵士音乐风格' },\n      { label: '古典', value: 'classical, orchestral, elegant', description: '古典音乐风格' },\n      { label: '中国风', value: 'chinese traditional, oriental, cultural', description: '中国传统风格' },\n    ]\n  },\n  tempo: {\n    title: '节奏',\n    icon: 'fas fa-tachometer-alt',\n    options: [\n      { label: '慢板', value: 'slow tempo, 60-80 BPM', description: '缓慢的节奏' },\n      { label: '中板', value: 'medium tempo, 90-120 BPM', description: '中等的节奏' },\n      { label: '快板', value: 'fast tempo, 130-160 BPM', description: '快速的节奏' },\n      { label: '极快', value: 'very fast tempo, 170+ BPM', description: '非常快的节奏' },\n    ]\n  },\n  instruments: {\n    title: '乐器',\n    icon: 'fas fa-guitar',\n    options: [\n      { label: '钢琴', value: 'piano, keys', description: '钢琴演奏' },\n      { label: '吉他', value: 'guitar, acoustic guitar', description: '吉他演奏' },\n      { label: '小提琴', value: 'violin, strings', description: '小提琴演奏' },\n      { label: '鼓组', value: 'drums, percussion', description: '鼓组节奏' },\n      { label: '贝斯', value: 'bass, bass guitar', description: '贝斯低音' },\n      { label: '合成器', value: 'synthesizer, electronic', description: '合成器音色' },\n      { label: '管弦乐', value: 'orchestra, symphonic', description: '管弦乐团' },\n      { label: '中国乐器', value: 'chinese instruments, erhu, guzheng', description: '中国传统乐器' },\n    ]\n  }\n};\n\nexport default function SongDescriptionSelector({\n  value,\n  onChange,\n  className = \"\"\n}: SongDescriptionSelectorProps) {\n  const [selectedPresets, setSelectedPresets] = useState<string[]>([]);\n\n  const handlePresetToggle = (presetValue: string) => {\n    const newPresets = selectedPresets.includes(presetValue)\n      ? selectedPresets.filter(p => p !== presetValue)\n      : [...selectedPresets, presetValue];\n    \n    setSelectedPresets(newPresets);\n    \n    // 更新描述文本\n    const newDescription = newPresets.join(', ');\n    onChange(newDescription);\n  };\n\n  const handleTextChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {\n    onChange(event.target.value);\n    // 清空预设选择状态，因为用户手动编辑了\n    setSelectedPresets([]);\n  };\n\n  const insertPreset = (presetValue: string) => {\n    const newValue = value ? `${value}, ${presetValue}` : presetValue;\n    onChange(newValue);\n  };\n\n  const clearAll = () => {\n    onChange('');\n    setSelectedPresets([]);\n  };\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* 预设选项 */}\n      <div className=\"space-y-4\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold flex items-center\">\n            <i className=\"fas fa-palette mr-2 text-primary\"></i>\n            预设描述选项\n          </h3>\n          <button\n            onClick={clearAll}\n            className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n          >\n            <i className=\"fas fa-eraser mr-1\"></i>\n            清空所有\n          </button>\n        </div>\n\n        {Object.entries(PRESET_DESCRIPTIONS).map(([key, category]) => (\n          <div key={key} className=\"space-y-2\">\n            <h4 className=\"text-sm font-medium text-muted-foreground flex items-center\">\n              <i className={`${category.icon} mr-2`}></i>\n              {category.title}\n            </h4>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2\">\n              {category.options.map((option) => (\n                <button\n                  key={option.value}\n                  onClick={() => insertPreset(option.value)}\n                  className=\"p-3 text-left bg-muted/20 hover:bg-muted/40 rounded-lg border border-muted/30 hover:border-primary/50 transition-all duration-200 group\"\n                  title={option.description}\n                >\n                  <div className=\"text-sm font-medium group-hover:text-primary transition-colors\">\n                    {option.label}\n                  </div>\n                  <div className=\"text-xs text-muted-foreground mt-1\">\n                    {option.description}\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* 自定义文本输入 */}\n      <div className=\"space-y-3\">\n        <label className=\"text-sm font-medium flex items-center\">\n          <i className=\"fas fa-edit mr-2 text-primary\"></i>\n          自定义描述 (英文)\n        </label>\n        <textarea\n          value={value}\n          onChange={handleTextChange}\n          placeholder=\"描述歌曲的性别、音色、风格、情感、乐器和BPM。例如：female vocals, pop, happy, piano, 120 BPM\"\n          className=\"w-full h-24 p-3 bg-muted/20 border border-muted/30 rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all resize-none\"\n        />\n        <div className=\"text-xs text-muted-foreground\">\n          字符数: {value.length} • 建议使用英文描述以获得更好的效果\n        </div>\n      </div>\n\n      {/* 使用提示 */}\n      <div className=\"p-4 bg-accent/10 rounded-lg border border-accent/20\">\n        <div className=\"text-sm text-accent-foreground\">\n          <i className=\"fas fa-lightbulb mr-2\"></i>\n          <strong>使用提示：</strong>\n        </div>\n        <div className=\"text-xs text-muted-foreground mt-2 space-y-1\">\n          <div>• 点击预设选项可快速插入常用描述</div>\n          <div>• 可以组合多个预设选项创建复合描述</div>\n          <div>• 支持手动编辑和自定义描述内容</div>\n          <div>• 建议包含：性别、风格、情感、主要乐器、节奏等信息</div>\n          <div>• 使用英文描述可以获得更准确的生成效果</div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAUA,YAAY;AACZ,MAAM,sBAAsB;IAC1B,QAAQ;QACN,OAAO;QACP,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAM,OAAO;gBAAe,aAAa;YAAS;YAC3D;gBAAE,OAAO;gBAAM,OAAO;gBAAiB,aAAa;YAAS;YAC7D;gBAAE,OAAO;gBAAM,OAAO;gBAAgB,aAAa;YAAS;YAC5D;gBAAE,OAAO;gBAAM,OAAO;gBAAmB,aAAa;YAAS;SAChE;IACH;IACA,MAAM;QACJ,OAAO;QACP,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAM,OAAO;gBAAyB,aAAa;YAAU;YACtE;gBAAE,OAAO;gBAAM,OAAO;gBAA+B,aAAa;YAAU;YAC5E;gBAAE,OAAO;gBAAM,OAAO;gBAA0B,aAAa;YAAU;YACvE;gBAAE,OAAO;gBAAM,OAAO;gBAAmC,aAAa;YAAU;YAChF;gBAAE,OAAO;gBAAM,OAAO;gBAA0B,aAAa;YAAU;YACvE;gBAAE,OAAO;gBAAM,OAAO;gBAAiC,aAAa;YAAU;SAC/E;IACH;IACA,OAAO;QACL,OAAO;QACP,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAM,OAAO;gBAA2B,aAAa;YAAS;YACvE;gBAAE,OAAO;gBAAM,OAAO;gBAAgC,aAAa;YAAS;YAC5E;gBAAE,OAAO;gBAAM,OAAO;gBAAgC,aAAa;YAAS;YAC5E;gBAAE,OAAO;gBAAM,OAAO;gBAAoC,aAAa;YAAS;YAChF;gBAAE,OAAO;gBAAM,OAAO;gBAA8B,aAAa;YAAS;YAC1E;gBAAE,OAAO;gBAAM,OAAO;gBAAkC,aAAa;YAAS;YAC9E;gBAAE,OAAO;gBAAO,OAAO;gBAA2C,aAAa;YAAS;SACzF;IACH;IACA,OAAO;QACL,OAAO;QACP,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAM,OAAO;gBAAyB,aAAa;YAAQ;YACpE;gBAAE,OAAO;gBAAM,OAAO;gBAA4B,aAAa;YAAQ;YACvE;gBAAE,OAAO;gBAAM,OAAO;gBAA2B,aAAa;YAAQ;YACtE;gBAAE,OAAO;gBAAM,OAAO;gBAA6B,aAAa;YAAS;SAC1E;IACH;IACA,aAAa;QACX,OAAO;QACP,MAAM;QACN,SAAS;YACP;gBAAE,OAAO;gBAAM,OAAO;gBAAe,aAAa;YAAO;YACzD;gBAAE,OAAO;gBAAM,OAAO;gBAA2B,aAAa;YAAO;YACrE;gBAAE,OAAO;gBAAO,OAAO;gBAAmB,aAAa;YAAQ;YAC/D;gBAAE,OAAO;gBAAM,OAAO;gBAAqB,aAAa;YAAO;YAC/D;gBAAE,OAAO;gBAAM,OAAO;gBAAqB,aAAa;YAAO;YAC/D;gBAAE,OAAO;gBAAO,OAAO;gBAA2B,aAAa;YAAQ;YACvE;gBAAE,OAAO;gBAAO,OAAO;gBAAwB,aAAa;YAAO;YACnE;gBAAE,OAAO;gBAAQ,OAAO;gBAAsC,aAAa;YAAS;SACrF;IACH;AACF;AAEe,SAAS,wBAAwB,EAC9C,KAAK,EACL,QAAQ,EACR,YAAY,EAAE,EACe;;IAC7B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnE,MAAM,qBAAqB,CAAC;QAC1B,MAAM,aAAa,gBAAgB,QAAQ,CAAC,eACxC,gBAAgB,MAAM,CAAC,CAAA,IAAK,MAAM,eAClC;eAAI;YAAiB;SAAY;QAErC,mBAAmB;QAEnB,SAAS;QACT,MAAM,iBAAiB,WAAW,IAAI,CAAC;QACvC,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS,MAAM,MAAM,CAAC,KAAK;QAC3B,qBAAqB;QACrB,mBAAmB,EAAE;IACvB;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,QAAQ,GAAG,MAAM,EAAE,EAAE,aAAa,GAAG;QACtD,SAAS;IACX;IAEA,MAAM,WAAW;QACf,SAAS;QACT,mBAAmB,EAAE;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAE,WAAU;;;;;;oCAAuC;;;;;;;0CAGtD,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC;wCAAE,WAAU;;;;;;oCAAyB;;;;;;;;;;;;;oBAKzC,OAAO,OAAO,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS,iBACvD,6LAAC;4BAAc,WAAU;;8CACvB,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAE,WAAW,GAAG,SAAS,IAAI,CAAC,KAAK,CAAC;;;;;;wCACpC,SAAS,KAAK;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;8CACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,uBACrB,6LAAC;4CAEC,SAAS,IAAM,aAAa,OAAO,KAAK;4CACxC,WAAU;4CACV,OAAO,OAAO,WAAW;;8DAEzB,6LAAC;oDAAI,WAAU;8DACZ,OAAO,KAAK;;;;;;8DAEf,6LAAC;oDAAI,WAAU;8DACZ,OAAO,WAAW;;;;;;;2CAThB,OAAO,KAAK;;;;;;;;;;;2BARf;;;;;;;;;;;0BA2Bd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAE,WAAU;;;;;;4BAAoC;;;;;;;kCAGnD,6LAAC;wBACC,OAAO;wBACP,UAAU;wBACV,aAAY;wBACZ,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;4BAAgC;4BACvC,MAAM,MAAM;4BAAC;;;;;;;;;;;;;0BAKvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;0CAAO;;;;;;;;;;;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAI;;;;;;0CACL,6LAAC;0CAAI;;;;;;0CACL,6LAAC;0CAAI;;;;;;0CACL,6LAAC;0CAAI;;;;;;0CACL,6LAAC;0CAAI;;;;;;;;;;;;;;;;;;;;;;;;AAKf;GAjHwB;KAAA", "debugId": null}}, {"offset": {"line": 1209, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/nodeWork/music/music-generation-app/src/components/AudioPlayer.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\n\ninterface AudioPlayerProps {\n  src?: string;\n  title?: string;\n  artist?: string;\n  onLoadStart?: () => void;\n  onLoadEnd?: () => void;\n  onError?: (error: string) => void;\n  className?: string;\n}\n\nexport default function AudioPlayer({\n  src,\n  title = \"生成的音乐\",\n  artist = \"AI音乐创作\",\n  onLoadStart,\n  onLoadEnd,\n  onError,\n  className = \"\"\n}: AudioPlayerProps) {\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [currentTime, setCurrentTime] = useState(0);\n  const [duration, setDuration] = useState(0);\n  const [volume, setVolume] = useState(1);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string>(\"\");\n  \n  const audioRef = useRef<HTMLAudioElement>(null);\n  const progressRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    const handleLoadStart = () => {\n      setIsLoading(true);\n      setError(\"\");\n      onLoadStart?.();\n    };\n\n    const handleLoadedData = () => {\n      setIsLoading(false);\n      setDuration(audio.duration);\n      onLoadEnd?.();\n    };\n\n    const handleTimeUpdate = () => {\n      setCurrentTime(audio.currentTime);\n    };\n\n    const handleEnded = () => {\n      setIsPlaying(false);\n      setCurrentTime(0);\n    };\n\n    const handleError = () => {\n      setIsLoading(false);\n      const errorMsg = \"音频加载失败\";\n      setError(errorMsg);\n      onError?.(errorMsg);\n    };\n\n    audio.addEventListener('loadstart', handleLoadStart);\n    audio.addEventListener('loadeddata', handleLoadedData);\n    audio.addEventListener('timeupdate', handleTimeUpdate);\n    audio.addEventListener('ended', handleEnded);\n    audio.addEventListener('error', handleError);\n\n    return () => {\n      audio.removeEventListener('loadstart', handleLoadStart);\n      audio.removeEventListener('loadeddata', handleLoadedData);\n      audio.removeEventListener('timeupdate', handleTimeUpdate);\n      audio.removeEventListener('ended', handleEnded);\n      audio.removeEventListener('error', handleError);\n    };\n  }, [onLoadStart, onLoadEnd, onError]);\n\n  const togglePlay = () => {\n    const audio = audioRef.current;\n    if (!audio || !src) return;\n\n    if (isPlaying) {\n      audio.pause();\n    } else {\n      audio.play();\n    }\n    setIsPlaying(!isPlaying);\n  };\n\n  const handleProgressClick = (event: React.MouseEvent<HTMLDivElement>) => {\n    const audio = audioRef.current;\n    const progressBar = progressRef.current;\n    if (!audio || !progressBar || !duration) return;\n\n    const rect = progressBar.getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const newTime = (clickX / rect.width) * duration;\n    \n    audio.currentTime = newTime;\n    setCurrentTime(newTime);\n  };\n\n  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const newVolume = parseFloat(event.target.value);\n    setVolume(newVolume);\n    \n    const audio = audioRef.current;\n    if (audio) {\n      audio.volume = newVolume;\n    }\n  };\n\n  const formatTime = (time: number): string => {\n    if (isNaN(time)) return \"0:00\";\n    \n    const minutes = Math.floor(time / 60);\n    const seconds = Math.floor(time % 60);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  };\n\n  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;\n\n  if (!src) {\n    return (\n      <div className={`audio-player p-6 text-center ${className}`}>\n        <div className=\"flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-muted/20 rounded-full\">\n          <i className=\"fas fa-music text-2xl text-muted-foreground\"></i>\n        </div>\n        <p className=\"text-muted-foreground\">暂无音频文件</p>\n        <p className=\"text-sm text-muted-foreground mt-1\">生成音乐后将在此处播放</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`audio-player p-6 ${className}`}>\n      <audio ref={audioRef} src={src} preload=\"metadata\" />\n      \n      {error && (\n        <div className=\"mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-600 text-sm\">\n          <i className=\"fas fa-exclamation-circle mr-2\"></i>\n          {error}\n        </div>\n      )}\n\n      {/* 音乐信息 */}\n      <div className=\"flex items-center mb-6\">\n        <div className=\"flex items-center justify-center w-12 h-12 bg-primary/20 rounded-lg mr-4\">\n          <i className=\"fas fa-music text-primary\"></i>\n        </div>\n        <div className=\"flex-1\">\n          <h3 className=\"font-semibold text-lg\">{title}</h3>\n          <p className=\"text-sm text-muted-foreground\">{artist}</p>\n        </div>\n        <div className=\"text-sm text-muted-foreground\">\n          {formatTime(currentTime)} / {formatTime(duration)}\n        </div>\n      </div>\n\n      {/* 进度条 */}\n      <div className=\"mb-6\">\n        <div\n          ref={progressRef}\n          className=\"relative h-2 bg-muted/30 rounded-full cursor-pointer group\"\n          onClick={handleProgressClick}\n        >\n          <div\n            className=\"absolute top-0 left-0 h-full bg-gradient-to-r from-primary to-secondary rounded-full transition-all duration-150\"\n            style={{ width: `${progressPercentage}%` }}\n          />\n          <div\n            className=\"absolute top-1/2 w-4 h-4 bg-white border-2 border-primary rounded-full transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity shadow-lg\"\n            style={{ left: `calc(${progressPercentage}% - 8px)` }}\n          />\n        </div>\n      </div>\n\n      {/* 控制按钮 */}\n      <div className=\"flex items-center justify-center space-x-4 mb-4\">\n        <button\n          onClick={() => {\n            const audio = audioRef.current;\n            if (audio) {\n              audio.currentTime = Math.max(0, audio.currentTime - 10);\n            }\n          }}\n          className=\"p-2 rounded-full bg-muted/20 hover:bg-muted/40 transition-colors\"\n          disabled={!src || isLoading}\n        >\n          <i className=\"fas fa-backward text-lg\"></i>\n        </button>\n\n        <button\n          onClick={togglePlay}\n          disabled={!src || isLoading}\n          className=\"p-4 rounded-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isLoading ? (\n            <i className=\"fas fa-spinner fa-spin text-xl\"></i>\n          ) : isPlaying ? (\n            <i className=\"fas fa-pause text-xl\"></i>\n          ) : (\n            <i className=\"fas fa-play text-xl\"></i>\n          )}\n        </button>\n\n        <button\n          onClick={() => {\n            const audio = audioRef.current;\n            if (audio) {\n              audio.currentTime = Math.min(duration, audio.currentTime + 10);\n            }\n          }}\n          className=\"p-2 rounded-full bg-muted/20 hover:bg-muted/40 transition-colors\"\n          disabled={!src || isLoading}\n        >\n          <i className=\"fas fa-forward text-lg\"></i>\n        </button>\n      </div>\n\n      {/* 音量控制 */}\n      <div className=\"flex items-center space-x-3\">\n        <i className=\"fas fa-volume-down text-muted-foreground\"></i>\n        <input\n          type=\"range\"\n          min=\"0\"\n          max=\"1\"\n          step=\"0.1\"\n          value={volume}\n          onChange={handleVolumeChange}\n          className=\"flex-1 h-1 bg-muted/30 rounded-lg appearance-none cursor-pointer\"\n        />\n        <i className=\"fas fa-volume-up text-muted-foreground\"></i>\n        <span className=\"text-sm text-muted-foreground w-8\">\n          {Math.round(volume * 100)}%\n        </span>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAce,SAAS,YAAY,EAClC,GAAG,EACH,QAAQ,OAAO,EACf,SAAS,QAAQ,EACjB,WAAW,EACX,SAAS,EACT,OAAO,EACP,YAAY,EAAE,EACG;;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,QAAQ,SAAS,OAAO;YAC9B,IAAI,CAAC,OAAO;YAEZ,MAAM;yDAAkB;oBACtB,aAAa;oBACb,SAAS;oBACT;gBACF;;YAEA,MAAM;0DAAmB;oBACvB,aAAa;oBACb,YAAY,MAAM,QAAQ;oBAC1B;gBACF;;YAEA,MAAM;0DAAmB;oBACvB,eAAe,MAAM,WAAW;gBAClC;;YAEA,MAAM;qDAAc;oBAClB,aAAa;oBACb,eAAe;gBACjB;;YAEA,MAAM;qDAAc;oBAClB,aAAa;oBACb,MAAM,WAAW;oBACjB,SAAS;oBACT,UAAU;gBACZ;;YAEA,MAAM,gBAAgB,CAAC,aAAa;YACpC,MAAM,gBAAgB,CAAC,cAAc;YACrC,MAAM,gBAAgB,CAAC,cAAc;YACrC,MAAM,gBAAgB,CAAC,SAAS;YAChC,MAAM,gBAAgB,CAAC,SAAS;YAEhC;yCAAO;oBACL,MAAM,mBAAmB,CAAC,aAAa;oBACvC,MAAM,mBAAmB,CAAC,cAAc;oBACxC,MAAM,mBAAmB,CAAC,cAAc;oBACxC,MAAM,mBAAmB,CAAC,SAAS;oBACnC,MAAM,mBAAmB,CAAC,SAAS;gBACrC;;QACF;gCAAG;QAAC;QAAa;QAAW;KAAQ;IAEpC,MAAM,aAAa;QACjB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,SAAS,CAAC,KAAK;QAEpB,IAAI,WAAW;YACb,MAAM,KAAK;QACb,OAAO;YACL,MAAM,IAAI;QACZ;QACA,aAAa,CAAC;IAChB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,cAAc,YAAY,OAAO;QACvC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU;QAEzC,MAAM,OAAO,YAAY,qBAAqB;QAC9C,MAAM,SAAS,MAAM,OAAO,GAAG,KAAK,IAAI;QACxC,MAAM,UAAU,AAAC,SAAS,KAAK,KAAK,GAAI;QAExC,MAAM,WAAW,GAAG;QACpB,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,YAAY,WAAW,MAAM,MAAM,CAAC,KAAK;QAC/C,UAAU;QAEV,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,OAAO;YACT,MAAM,MAAM,GAAG;QACjB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,MAAM,OAAO,OAAO;QAExB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,OAAO,GAAG,QAAQ,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC5D;IAEA,MAAM,qBAAqB,WAAW,IAAI,AAAC,cAAc,WAAY,MAAM;IAE3E,IAAI,CAAC,KAAK;QACR,qBACE,6LAAC;YAAI,WAAW,CAAC,6BAA6B,EAAE,WAAW;;8BACzD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;;;;;;;;;;8BAEf,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;8BACrC,6LAAC;oBAAE,WAAU;8BAAqC;;;;;;;;;;;;IAGxD;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW;;0BAC7C,6LAAC;gBAAM,KAAK;gBAAU,KAAK;gBAAK,SAAQ;;;;;;YAEvC,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;0BAKL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;kCAEhD,6LAAC;wBAAI,WAAU;;4BACZ,WAAW;4BAAa;4BAAI,WAAW;;;;;;;;;;;;;0BAK5C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,KAAK;oBACL,WAAU;oBACV,SAAS;;sCAET,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;4BAAC;;;;;;sCAE3C,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,MAAM,CAAC,KAAK,EAAE,mBAAmB,QAAQ,CAAC;4BAAC;;;;;;;;;;;;;;;;;0BAM1D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;4BACP,MAAM,QAAQ,SAAS,OAAO;4BAC9B,IAAI,OAAO;gCACT,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;4BACtD;wBACF;wBACA,WAAU;wBACV,UAAU,CAAC,OAAO;kCAElB,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAGf,6LAAC;wBACC,SAAS;wBACT,UAAU,CAAC,OAAO;wBAClB,WAAU;kCAET,0BACC,6LAAC;4BAAE,WAAU;;;;;mCACX,0BACF,6LAAC;4BAAE,WAAU;;;;;iDAEb,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAIjB,6LAAC;wBACC,SAAS;4BACP,MAAM,QAAQ,SAAS,OAAO;4BAC9B,IAAI,OAAO;gCACT,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,UAAU,MAAM,WAAW,GAAG;4BAC7D;wBACF;wBACA,WAAU;wBACV,UAAU,CAAC,OAAO;kCAElB,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;0BAKjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;;;;;kCACb,6LAAC;wBACC,MAAK;wBACL,KAAI;wBACJ,KAAI;wBACJ,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,WAAU;;;;;;kCAEZ,6LAAC;wBAAE,WAAU;;;;;;kCACb,6LAAC;wBAAK,WAAU;;4BACb,KAAK,KAAK,CAAC,SAAS;4BAAK;;;;;;;;;;;;;;;;;;;AAKpC;GApOwB;KAAA", "debugId": null}}, {"offset": {"line": 1634, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/nodeWork/music/music-generation-app/src/lib/musicApi.ts"], "sourcesContent": ["import axios from 'axios';\n\n// Gradio API 配置\nconst GRADIO_API_URL = 'http://************:7860';\n\n// 音乐风格选项\nexport const MUSIC_GENRES = [\n  'Pop',\n  'R&B', \n  'Dance',\n  'Jazz',\n  'Folk',\n  'Rock',\n  'Chinese Style',\n  'Chinese Tradition',\n  'Metal',\n  'Reggae',\n  'Chinese Opera',\n  'Auto'\n] as const;\n\nexport type MusicGenre = typeof MUSIC_GENRES[number];\n\n// 生成音乐的参数接口\nexport interface GenerateMusicParams {\n  lyric: string;\n  description?: string;\n  prompt_audio?: File;\n  genre: MusicGenre;\n  cfg_coef: number;\n  temperature: number;\n  top_k: number;\n}\n\n// API 响应接口\nexport interface GenerateMusicResponse {\n  audio_url: string;\n  generation_info: {\n    duration?: number;\n    sample_rate?: number;\n    channels?: number;\n    format?: string;\n    [key: string]: any;\n  };\n}\n\n// 错误处理接口\nexport interface ApiError {\n  message: string;\n  code?: string;\n  details?: any;\n}\n\nclass MusicApiService {\n  private baseURL: string;\n\n  constructor() {\n    this.baseURL = GRADIO_API_URL;\n  }\n\n  /**\n   * 生成音乐\n   */\n  async generateMusic(params: GenerateMusicParams): Promise<GenerateMusicResponse> {\n    try {\n      // 确保歌词格式正确 - 按照后端期望的格式处理\n      let formattedLyric = params.lyric.trim();\n\n      // 按照原始app.py的格式处理逻辑\n      // 1. 替换标签格式\n      formattedLyric = formattedLyric\n        .replace(/\\[intro\\]/g, \"[intro-short]\")\n        .replace(/\\[inst\\]/g, \"[inst-short]\")\n        .replace(/\\[outro\\]/g, \"[outro-short]\");\n\n      // 2. 按段落分割（空行分隔）\n      const paragraphs = formattedLyric.split('\\n\\n').filter(p => p.trim());\n      const processedParagraphs: string[] = [];\n\n      for (const para of paragraphs) {\n        const lines = para.split('\\n').map(line => line.trim()).filter(line => line);\n        if (lines.length === 0) continue;\n\n        // 检查第一行是否是结构标签\n        const firstLine = lines[0];\n        let structTag = '';\n        let lyricsLines: string[] = [];\n\n        // 如果第一行包含结构标签，提取标签和歌词\n        const tagMatch = firstLine.match(/^(\\[[\\w-]+\\])\\s*(.*)/);\n        if (tagMatch) {\n          structTag = tagMatch[1];\n          const restOfFirstLine = tagMatch[2].trim();\n          lyricsLines = restOfFirstLine ? [restOfFirstLine, ...lines.slice(1)] : lines.slice(1);\n        } else {\n          // 如果没有标签，默认为verse\n          structTag = '[verse]';\n          lyricsLines = lines;\n        }\n\n        // 检查是否是有效的结构标签\n        const validTags = ['[verse]', '[chorus]', '[bridge]', '[intro-short]', '[intro-medium]', '[intro-long]', '[outro-short]', '[outro-medium]', '[outro-long]', '[inst-short]', '[inst-medium]', '[inst-long]', '[silence]'];\n\n        if (!validTags.includes(structTag.toLowerCase())) {\n          // 如果不是有效标签，默认为verse\n          structTag = '[verse]';\n          lyricsLines = lines;\n        }\n\n        // 处理有效的结构标签\n        const vocalStructs = ['[verse]', '[chorus]', '[bridge]'];\n        if (vocalStructs.includes(structTag.toLowerCase())) {\n          // 需要歌词的段落\n          if (lyricsLines.length === 0 || lyricsLines.every(line => !line.trim())) {\n            throw new Error(`${structTag} 段落需要包含歌词内容`);\n          }\n          // 清理歌词内容，移除特殊字符（按照app.py的逻辑）\n          const cleanedLyrics = lyricsLines\n            .map(line => line.replace(/[^\\w\\s\\[\\]\\-\\u4e00-\\u9fff\\u3040-\\u309f\\u30a0-\\u30ff\\uac00-\\ud7af\\u00c0-\\u017f]/g, \"\"))\n            .filter(line => line.trim())\n            .join('.');\n          processedParagraphs.push(`${structTag} ${cleanedLyrics}`);\n        } else {\n          // 不需要歌词的段落（intro, inst, outro等）\n          if (lyricsLines.length > 0 && lyricsLines.some(line => line.trim())) {\n            throw new Error(`${structTag} 段落不应包含歌词内容`);\n          }\n          processedParagraphs.push(structTag);\n        }\n      }\n\n      // 检查是否至少包含一个vocal段落\n      const hasVocalStruct = processedParagraphs.some(para =>\n        para.startsWith('[verse]') || para.startsWith('[chorus]') || para.startsWith('[bridge]')\n      );\n      if (!hasVocalStruct) {\n        throw new Error('歌词必须至少包含以下结构之一: [verse], [chorus], [bridge]');\n      }\n\n      // 3. 用分号连接段落（按照app.py的格式）\n      formattedLyric = processedParagraphs.join(' ; ');\n\n      // 准备Gradio API调用数据 - 确保所有数值参数都是正确的类型\n      const requestData = {\n        data: [\n          formattedLyric,                           // lyric (string)\n          params.description || null,               // description (string or null)\n          params.prompt_audio || null,              // prompt_audio (file or null)\n          params.genre || \"Pop\",                    // genre (string)\n          parseFloat(String(params.cfg_coef || 1.5)),      // cfg_coef (float)\n          parseFloat(String(params.temperature || 0.9)),   // temperature (float)\n          parseInt(String(params.top_k || 50), 10)         // top_k (integer)\n        ],\n        fn_index: 0  // 指定函数索引\n      };\n\n      console.log('Sending request to Gradio API:', requestData);\n      console.log('Formatted lyric:', JSON.stringify(formattedLyric));\n\n      // 使用正确的Gradio API端点\n      const response = await axios.post(\n        `${this.baseURL}/api/predict`,\n        requestData,\n        {\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          timeout: 300000, // 5分钟超时\n        }\n      );\n\n      console.log('Gradio API response:', response.data);\n\n      // 处理响应\n      if (response.data && response.data.data && Array.isArray(response.data.data)) {\n        const responseData = response.data.data;\n\n        if (responseData.length >= 1) {\n          const audioData = responseData[0];\n          const generationInfo = responseData[1] || {};\n\n          if (audioData === null) {\n            // 生成失败\n            const errorMsg = typeof generationInfo === 'string' ? generationInfo : '音乐生成失败';\n            throw new ApiError(errorMsg);\n          }\n\n          // 处理音频数据\n          let audioUrl = '';\n          if (audioData && Array.isArray(audioData) && audioData.length === 2) {\n            // audioData 格式: [sample_rate, audio_array]\n            const [sampleRate, audioArray] = audioData;\n\n            // 创建音频blob\n            const audioBlob = this.createAudioBlob(audioArray, sampleRate);\n            audioUrl = URL.createObjectURL(audioBlob);\n          } else if (typeof audioData === 'string') {\n            // 如果返回的是文件路径\n            audioUrl = audioData.startsWith('http')\n              ? audioData\n              : `${this.baseURL}/file=${audioData}`;\n          }\n\n          return {\n            audio_url: audioUrl,\n            generation_info: generationInfo || {}\n          };\n        }\n      }\n\n      throw new Error('Invalid response format from API');\n    } catch (error) {\n      console.error('Music generation error:', error);\n\n      if (error instanceof ApiError) {\n        throw error;\n      }\n\n      if (axios.isAxiosError(error)) {\n        if (error.code === 'ECONNABORTED') {\n          throw new ApiError('请求超时，音乐生成可能需要更长时间，请稍后重试');\n        } else if (error.response) {\n          const errorMsg = error.response.data?.error || error.response.statusText;\n          throw new ApiError(\n            `服务器错误 (${error.response.status}): ${errorMsg}`,\n            error.response.status.toString(),\n            error.response.data\n          );\n        } else if (error.request) {\n          throw new ApiError('无法连接到音乐生成服务，请检查网络连接');\n        }\n      }\n\n      throw new ApiError(\n        error instanceof Error ? error.message : '音乐生成失败，请重试'\n      );\n    }\n  }\n\n  /**\n   * 创建音频Blob对象\n   */\n  private createAudioBlob(audioArray: number[][], sampleRate: number): Blob {\n    // 将音频数组转换为WAV格式\n    const length = audioArray[0].length;\n    const numberOfChannels = audioArray.length;\n    const buffer = new ArrayBuffer(44 + length * numberOfChannels * 2);\n    const view = new DataView(buffer);\n\n    // WAV文件头\n    const writeString = (offset: number, string: string) => {\n      for (let i = 0; i < string.length; i++) {\n        view.setUint8(offset + i, string.charCodeAt(i));\n      }\n    };\n\n    writeString(0, 'RIFF');\n    view.setUint32(4, 36 + length * numberOfChannels * 2, true);\n    writeString(8, 'WAVE');\n    writeString(12, 'fmt ');\n    view.setUint32(16, 16, true);\n    view.setUint16(20, 1, true);\n    view.setUint16(22, numberOfChannels, true);\n    view.setUint32(24, sampleRate, true);\n    view.setUint32(28, sampleRate * numberOfChannels * 2, true);\n    view.setUint16(32, numberOfChannels * 2, true);\n    view.setUint16(34, 16, true);\n    writeString(36, 'data');\n    view.setUint32(40, length * numberOfChannels * 2, true);\n\n    // 音频数据\n    let offset = 44;\n    for (let i = 0; i < length; i++) {\n      for (let channel = 0; channel < numberOfChannels; channel++) {\n        const sample = Math.max(-1, Math.min(1, audioArray[channel][i]));\n        view.setInt16(offset, sample * 0x7FFF, true);\n        offset += 2;\n      }\n    }\n\n    return new Blob([buffer], { type: 'audio/wav' });\n  }\n\n  /**\n   * 检查服务状态\n   */\n  async checkServiceStatus(): Promise<boolean> {\n    try {\n      const response = await axios.get(`${this.baseURL}/`, {\n        timeout: 10000\n      });\n      return response.status === 200;\n    } catch (error) {\n      console.error('Service status check failed:', error);\n      return false;\n    }\n  }\n\n  /**\n   * 获取支持的音频格式\n   */\n  getSupportedAudioFormats(): string[] {\n    return [\n      'audio/mpeg',     // MP3\n      'audio/wav',      // WAV\n      'audio/flac',     // FLAC\n      'audio/aac',      // AAC\n      'audio/ogg',      // OGG\n      'audio/webm',     // WebM\n    ];\n  }\n\n  /**\n   * 验证音频文件\n   */\n  validateAudioFile(file: File): { valid: boolean; error?: string } {\n    const supportedFormats = this.getSupportedAudioFormats();\n    \n    if (!supportedFormats.includes(file.type)) {\n      return {\n        valid: false,\n        error: '不支持的音频格式，请使用 MP3、WAV、FLAC 或 AAC 格式'\n      };\n    }\n\n    // 检查文件大小 (最大 50MB)\n    const maxSize = 50 * 1024 * 1024;\n    if (file.size > maxSize) {\n      return {\n        valid: false,\n        error: '音频文件过大，请使用小于 50MB 的文件'\n      };\n    }\n\n    return { valid: true };\n  }\n\n  /**\n   * 获取默认生成参数\n   */\n  getDefaultParams(): Omit<GenerateMusicParams, 'lyric'> {\n    return {\n      genre: 'Pop',\n      cfg_coef: 1.5,\n      temperature: 0.9,\n      top_k: 50,\n    };\n  }\n}\n\n// 导出单例实例\nexport const musicApi = new MusicApiService();\n\n// 导出错误类\nexport class ApiError extends Error {\n  public code?: string;\n  public details?: any;\n\n  constructor(message: string, code?: string, details?: any) {\n    super(message);\n    this.name = 'ApiError';\n    this.code = code;\n    this.details = details;\n  }\n}\n\n// 工具函数：格式化文件大小\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n// 工具函数：格式化时长\nexport function formatDuration(seconds: number): string {\n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor((seconds % 3600) / 60);\n  const secs = Math.floor(seconds % 60);\n  \n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  } else {\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,gBAAgB;AAChB,MAAM,iBAAiB;AAGhB,MAAM,eAAe;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAkCD,MAAM;IACI,QAAgB;IAExB,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA;;GAEC,GACD,MAAM,cAAc,MAA2B,EAAkC;QAC/E,IAAI;YACF,yBAAyB;YACzB,IAAI,iBAAiB,OAAO,KAAK,CAAC,IAAI;YAEtC,oBAAoB;YACpB,YAAY;YACZ,iBAAiB,eACd,OAAO,CAAC,cAAc,iBACtB,OAAO,CAAC,aAAa,gBACrB,OAAO,CAAC,cAAc;YAEzB,iBAAiB;YACjB,MAAM,aAAa,eAAe,KAAK,CAAC,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI;YAClE,MAAM,sBAAgC,EAAE;YAExC,KAAK,MAAM,QAAQ,WAAY;gBAC7B,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ;gBACvE,IAAI,MAAM,MAAM,KAAK,GAAG;gBAExB,eAAe;gBACf,MAAM,YAAY,KAAK,CAAC,EAAE;gBAC1B,IAAI,YAAY;gBAChB,IAAI,cAAwB,EAAE;gBAE9B,sBAAsB;gBACtB,MAAM,WAAW,UAAU,KAAK,CAAC;gBACjC,IAAI,UAAU;oBACZ,YAAY,QAAQ,CAAC,EAAE;oBACvB,MAAM,kBAAkB,QAAQ,CAAC,EAAE,CAAC,IAAI;oBACxC,cAAc,kBAAkB;wBAAC;2BAAoB,MAAM,KAAK,CAAC;qBAAG,GAAG,MAAM,KAAK,CAAC;gBACrF,OAAO;oBACL,kBAAkB;oBAClB,YAAY;oBACZ,cAAc;gBAChB;gBAEA,eAAe;gBACf,MAAM,YAAY;oBAAC;oBAAW;oBAAY;oBAAY;oBAAiB;oBAAkB;oBAAgB;oBAAiB;oBAAkB;oBAAgB;oBAAgB;oBAAiB;oBAAe;iBAAY;gBAExN,IAAI,CAAC,UAAU,QAAQ,CAAC,UAAU,WAAW,KAAK;oBAChD,oBAAoB;oBACpB,YAAY;oBACZ,cAAc;gBAChB;gBAEA,YAAY;gBACZ,MAAM,eAAe;oBAAC;oBAAW;oBAAY;iBAAW;gBACxD,IAAI,aAAa,QAAQ,CAAC,UAAU,WAAW,KAAK;oBAClD,UAAU;oBACV,IAAI,YAAY,MAAM,KAAK,KAAK,YAAY,KAAK,CAAC,CAAA,OAAQ,CAAC,KAAK,IAAI,KAAK;wBACvE,MAAM,IAAI,MAAM,GAAG,UAAU,WAAW,CAAC;oBAC3C;oBACA,6BAA6B;oBAC7B,MAAM,gBAAgB,YACnB,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,mFAAmF,KAC5G,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,IACxB,IAAI,CAAC;oBACR,oBAAoB,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,eAAe;gBAC1D,OAAO;oBACL,gCAAgC;oBAChC,IAAI,YAAY,MAAM,GAAG,KAAK,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;wBACnE,MAAM,IAAI,MAAM,GAAG,UAAU,WAAW,CAAC;oBAC3C;oBACA,oBAAoB,IAAI,CAAC;gBAC3B;YACF;YAEA,oBAAoB;YACpB,MAAM,iBAAiB,oBAAoB,IAAI,CAAC,CAAA,OAC9C,KAAK,UAAU,CAAC,cAAc,KAAK,UAAU,CAAC,eAAe,KAAK,UAAU,CAAC;YAE/E,IAAI,CAAC,gBAAgB;gBACnB,MAAM,IAAI,MAAM;YAClB;YAEA,0BAA0B;YAC1B,iBAAiB,oBAAoB,IAAI,CAAC;YAE1C,qCAAqC;YACrC,MAAM,cAAc;gBAClB,MAAM;oBACJ;oBACA,OAAO,WAAW,IAAI;oBACtB,OAAO,YAAY,IAAI;oBACvB,OAAO,KAAK,IAAI;oBAChB,WAAW,OAAO,OAAO,QAAQ,IAAI;oBACrC,WAAW,OAAO,OAAO,WAAW,IAAI;oBACxC,SAAS,OAAO,OAAO,KAAK,IAAI,KAAK,IAAY,kBAAkB;iBACpE;gBACD,UAAU,EAAG,SAAS;YACxB;YAEA,QAAQ,GAAG,CAAC,kCAAkC;YAC9C,QAAQ,GAAG,CAAC,oBAAoB,KAAK,SAAS,CAAC;YAE/C,oBAAoB;YACpB,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAC7B,aACA;gBACE,SAAS;oBACP,gBAAgB;gBAClB;gBACA,SAAS;YACX;YAGF,QAAQ,GAAG,CAAC,wBAAwB,SAAS,IAAI;YAEjD,OAAO;YACP,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;gBAC5E,MAAM,eAAe,SAAS,IAAI,CAAC,IAAI;gBAEvC,IAAI,aAAa,MAAM,IAAI,GAAG;oBAC5B,MAAM,YAAY,YAAY,CAAC,EAAE;oBACjC,MAAM,iBAAiB,YAAY,CAAC,EAAE,IAAI,CAAC;oBAE3C,IAAI,cAAc,MAAM;wBACtB,OAAO;wBACP,MAAM,WAAW,OAAO,mBAAmB,WAAW,iBAAiB;wBACvE,MAAM,IAAI,SAAS;oBACrB;oBAEA,SAAS;oBACT,IAAI,WAAW;oBACf,IAAI,aAAa,MAAM,OAAO,CAAC,cAAc,UAAU,MAAM,KAAK,GAAG;wBACnE,2CAA2C;wBAC3C,MAAM,CAAC,YAAY,WAAW,GAAG;wBAEjC,WAAW;wBACX,MAAM,YAAY,IAAI,CAAC,eAAe,CAAC,YAAY;wBACnD,WAAW,IAAI,eAAe,CAAC;oBACjC,OAAO,IAAI,OAAO,cAAc,UAAU;wBACxC,aAAa;wBACb,WAAW,UAAU,UAAU,CAAC,UAC5B,YACA,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW;oBACzC;oBAEA,OAAO;wBACL,WAAW;wBACX,iBAAiB,kBAAkB,CAAC;oBACtC;gBACF;YACF;YAEA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,IAAI,iBAAiB,UAAU;gBAC7B,MAAM;YACR;YAEA,IAAI,wIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,QAAQ;gBAC7B,IAAI,MAAM,IAAI,KAAK,gBAAgB;oBACjC,MAAM,IAAI,SAAS;gBACrB,OAAO,IAAI,MAAM,QAAQ,EAAE;oBACzB,MAAM,WAAW,MAAM,QAAQ,CAAC,IAAI,EAAE,SAAS,MAAM,QAAQ,CAAC,UAAU;oBACxE,MAAM,IAAI,SACR,CAAC,OAAO,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,UAAU,EAC/C,MAAM,QAAQ,CAAC,MAAM,CAAC,QAAQ,IAC9B,MAAM,QAAQ,CAAC,IAAI;gBAEvB,OAAO,IAAI,MAAM,OAAO,EAAE;oBACxB,MAAM,IAAI,SAAS;gBACrB;YACF;YAEA,MAAM,IAAI,SACR,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE7C;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,UAAsB,EAAE,UAAkB,EAAQ;QACxE,gBAAgB;QAChB,MAAM,SAAS,UAAU,CAAC,EAAE,CAAC,MAAM;QACnC,MAAM,mBAAmB,WAAW,MAAM;QAC1C,MAAM,SAAS,IAAI,YAAY,KAAK,SAAS,mBAAmB;QAChE,MAAM,OAAO,IAAI,SAAS;QAE1B,SAAS;QACT,MAAM,cAAc,CAAC,QAAgB;YACnC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,KAAK,QAAQ,CAAC,SAAS,GAAG,OAAO,UAAU,CAAC;YAC9C;QACF;QAEA,YAAY,GAAG;QACf,KAAK,SAAS,CAAC,GAAG,KAAK,SAAS,mBAAmB,GAAG;QACtD,YAAY,GAAG;QACf,YAAY,IAAI;QAChB,KAAK,SAAS,CAAC,IAAI,IAAI;QACvB,KAAK,SAAS,CAAC,IAAI,GAAG;QACtB,KAAK,SAAS,CAAC,IAAI,kBAAkB;QACrC,KAAK,SAAS,CAAC,IAAI,YAAY;QAC/B,KAAK,SAAS,CAAC,IAAI,aAAa,mBAAmB,GAAG;QACtD,KAAK,SAAS,CAAC,IAAI,mBAAmB,GAAG;QACzC,KAAK,SAAS,CAAC,IAAI,IAAI;QACvB,YAAY,IAAI;QAChB,KAAK,SAAS,CAAC,IAAI,SAAS,mBAAmB,GAAG;QAElD,OAAO;QACP,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC/B,IAAK,IAAI,UAAU,GAAG,UAAU,kBAAkB,UAAW;gBAC3D,MAAM,SAAS,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,EAAE;gBAC9D,KAAK,QAAQ,CAAC,QAAQ,SAAS,QAAQ;gBACvC,UAAU;YACZ;QACF;QAEA,OAAO,IAAI,KAAK;YAAC;SAAO,EAAE;YAAE,MAAM;QAAY;IAChD;IAEA;;GAEC,GACD,MAAM,qBAAuC;QAC3C,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBACnD,SAAS;YACX;YACA,OAAO,SAAS,MAAM,KAAK;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,2BAAqC;QACnC,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA;;GAEC,GACD,kBAAkB,IAAU,EAAsC;QAChE,MAAM,mBAAmB,IAAI,CAAC,wBAAwB;QAEtD,IAAI,CAAC,iBAAiB,QAAQ,CAAC,KAAK,IAAI,GAAG;YACzC,OAAO;gBACL,OAAO;gBACP,OAAO;YACT;QACF;QAEA,mBAAmB;QACnB,MAAM,UAAU,KAAK,OAAO;QAC5B,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO;gBACL,OAAO;gBACP,OAAO;YACT;QACF;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA;;GAEC,GACD,mBAAuD;QACrD,OAAO;YACL,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;QACT;IACF;AACF;AAGO,MAAM,WAAW,IAAI;AAGrB,MAAM,iBAAiB;IACrB,KAAc;IACd,QAAc;IAErB,YAAY,OAAe,EAAE,IAAa,EAAE,OAAa,CAAE;QACzD,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;IACjB;AACF;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;IAElC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC9F,OAAO;QACL,OAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACzD;AACF", "debugId": null}}, {"offset": {"line": 1960, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/code/nodeWork/music/music-generation-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport RichTextEditor from '@/components/RichTextEditor';\nimport AudioUpload from '@/components/AudioUpload';\nimport SongDescriptionSelector from '@/components/SongDescriptionSelector';\nimport AudioPlayer from '@/components/AudioPlayer';\nimport { musicApi, MUSIC_GENRES, type MusicGenre, type GenerateMusicParams, ApiError } from '@/lib/musicApi';\n\nexport default function Home() {\n  // 表单状态\n  const [lyrics, setLyrics] = useState('');\n  const [description, setDescription] = useState('');\n  const [audioFile, setAudioFile] = useState<File | null>(null);\n  const [selectedGenre, setSelectedGenre] = useState<MusicGenre>('Pop');\n  const [advancedSettings, setAdvancedSettings] = useState({\n    cfgCoef: 1.5,\n    temperature: 0.9,\n    topK: 50,\n  });\n  const [showAdvanced, setShowAdvanced] = useState(false);\n\n  // 生成状态\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generatedAudio, setGeneratedAudio] = useState<string>('');\n  const [generationInfo, setGenerationInfo] = useState<any>(null);\n  const [error, setError] = useState<string>('');\n\n  // 处理音乐生成\n  const handleGenerate = async () => {\n    if (!lyrics.trim()) {\n      setError('请输入歌词内容');\n      return;\n    }\n\n    setIsGenerating(true);\n    setError('');\n    setGeneratedAudio('');\n\n    try {\n      const params: GenerateMusicParams = {\n        lyric: lyrics,\n        description: description || undefined,\n        prompt_audio: audioFile || undefined,\n        genre: selectedGenre,\n        cfg_coef: advancedSettings.cfgCoef,\n        temperature: advancedSettings.temperature,\n        top_k: advancedSettings.topK,\n      };\n\n      const result = await musicApi.generateMusic(params);\n      setGeneratedAudio(result.audio_url);\n      setGenerationInfo(result.generation_info);\n    } catch (err) {\n      if (err instanceof ApiError) {\n        setError(err.message);\n      } else {\n        setError('音乐生成失败，请重试');\n      }\n      console.error('Generation error:', err);\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  // 重置表单\n  const handleReset = () => {\n    setLyrics('');\n    setDescription('');\n    setAudioFile(null);\n    setSelectedGenre('Pop');\n    setAdvancedSettings({\n      cfgCoef: 1.5,\n      temperature: 0.9,\n      topK: 50,\n    });\n    setGeneratedAudio('');\n    setGenerationInfo(null);\n    setError('');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* 头部 */}\n      <header className=\"border-b border-muted/20 bg-background/80 backdrop-blur-sm sticky top-0 z-50\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-primary rounded-lg\">\n                <i className=\"fas fa-music text-white text-lg\"></i>\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent\">\n                  AI音乐创作平台\n                </h1>\n                <p className=\"text-sm text-muted-foreground\">基于人工智能的音乐生成服务</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={handleReset}\n                className=\"px-4 py-2 text-sm bg-muted/20 hover:bg-muted/40 rounded-lg transition-colors\"\n              >\n                <i className=\"fas fa-refresh mr-2\"></i>\n                重置\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* 左侧：输入区域 */}\n          <div className=\"space-y-6\">\n            {/* 歌词输入 */}\n            <div className=\"card p-6\">\n              <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n                <i className=\"fas fa-pen-fancy mr-3 text-primary\"></i>\n                歌词创作\n              </h2>\n              <RichTextEditor\n                value={lyrics}\n                onChange={setLyrics}\n                placeholder=\"请输入您的歌词内容...\"\n                maxLength={2000}\n              />\n            </div>\n\n            {/* 选项卡 */}\n            <div className=\"card\">\n              <div className=\"border-b border-muted/20\">\n                <nav className=\"flex space-x-8 px-6\">\n                  <button className=\"py-4 border-b-2 border-primary text-primary font-medium\">\n                    风格选择\n                  </button>\n                  <button className=\"py-4 border-b-2 border-transparent text-muted-foreground hover:text-foreground font-medium\">\n                    音频提示\n                  </button>\n                  <button className=\"py-4 border-b-2 border-transparent text-muted-foreground hover:text-foreground font-medium\">\n                    文本提示\n                  </button>\n                </nav>\n              </div>\n\n              <div className=\"p-6\">\n                <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n                  <i className=\"fas fa-palette mr-3 text-primary\"></i>\n                  音乐风格\n                </h3>\n                <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n                  {MUSIC_GENRES.map((genre) => (\n                    <button\n                      key={genre}\n                      onClick={() => setSelectedGenre(genre)}\n                      className={`p-3 rounded-lg border transition-all ${\n                        selectedGenre === genre\n                          ? 'border-primary bg-primary/10 text-primary'\n                          : 'border-muted/30 hover:border-primary/50 hover:bg-primary/5'\n                      }`}\n                    >\n                      {genre === 'Chinese Style' ? '中国风' :\n                       genre === 'Chinese Tradition' ? '传统' :\n                       genre === 'Chinese Opera' ? '戏曲' :\n                       genre === 'R&B' ? '节奏布鲁斯' :\n                       genre === 'Pop' ? '流行' :\n                       genre === 'Dance' ? '舞曲' :\n                       genre === 'Jazz' ? '爵士' :\n                       genre === 'Folk' ? '民谣' :\n                       genre === 'Rock' ? '摇滚' :\n                       genre === 'Metal' ? '金属' :\n                       genre === 'Reggae' ? '雷鬼' :\n                       genre === 'Auto' ? '自动' : genre}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* 音频上传 */}\n            <div className=\"card p-6\">\n              <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n                <i className=\"fas fa-upload mr-3 text-primary\"></i>\n                音频提示 (可选)\n              </h2>\n              <AudioUpload\n                onFileSelect={setAudioFile}\n                maxSize={50}\n              />\n            </div>\n\n            {/* 歌曲描述 */}\n            <div className=\"card p-6\">\n              <h2 className=\"text-xl font-semibold mb-4 flex items-center\">\n                <i className=\"fas fa-edit mr-3 text-primary\"></i>\n                歌曲描述 (可选)\n              </h2>\n              <SongDescriptionSelector\n                value={description}\n                onChange={setDescription}\n              />\n            </div>\n\n            {/* 高级设置 */}\n            <div className=\"card p-6\">\n              <button\n                onClick={() => setShowAdvanced(!showAdvanced)}\n                className=\"w-full flex items-center justify-between text-lg font-semibold mb-4\"\n              >\n                <div className=\"flex items-center\">\n                  <i className=\"fas fa-cog mr-3 text-primary\"></i>\n                  高级设置\n                </div>\n                <i className={`fas fa-chevron-${showAdvanced ? 'up' : 'down'} text-muted-foreground`}></i>\n              </button>\n\n              {showAdvanced && (\n                <div className=\"space-y-4 pt-4 border-t border-muted/20\">\n                  <div>\n                    <label className=\"block text-sm font-medium mb-2\">\n                      CFG系数: {advancedSettings.cfgCoef}\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"1\"\n                      max=\"3\"\n                      step=\"0.1\"\n                      value={advancedSettings.cfgCoef}\n                      onChange={(e) => setAdvancedSettings(prev => ({\n                        ...prev,\n                        cfgCoef: parseFloat(e.target.value)\n                      }))}\n                      className=\"w-full h-2 bg-muted/30 rounded-lg appearance-none cursor-pointer\"\n                    />\n                    <div className=\"text-xs text-muted-foreground mt-1\">\n                      控制生成结果与提示的匹配程度\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium mb-2\">\n                      温度: {advancedSettings.temperature}\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"0.1\"\n                      max=\"1.5\"\n                      step=\"0.1\"\n                      value={advancedSettings.temperature}\n                      onChange={(e) => setAdvancedSettings(prev => ({\n                        ...prev,\n                        temperature: parseFloat(e.target.value)\n                      }))}\n                      className=\"w-full h-2 bg-muted/30 rounded-lg appearance-none cursor-pointer\"\n                    />\n                    <div className=\"text-xs text-muted-foreground mt-1\">\n                      控制生成结果的随机性和创造性\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium mb-2\">\n                      Top-K: {advancedSettings.topK}\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"10\"\n                      max=\"100\"\n                      step=\"10\"\n                      value={advancedSettings.topK}\n                      onChange={(e) => setAdvancedSettings(prev => ({\n                        ...prev,\n                        topK: parseInt(e.target.value)\n                      }))}\n                      className=\"w-full h-2 bg-muted/30 rounded-lg appearance-none cursor-pointer\"\n                    />\n                    <div className=\"text-xs text-muted-foreground mt-1\">\n                      限制候选词汇的数量，影响生成质量\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 右侧：播放器和控制 */}\n          <div className=\"space-y-6\">\n            {/* 生成按钮 */}\n            <div className=\"card p-6\">\n              <button\n                onClick={handleGenerate}\n                disabled={isGenerating || !lyrics.trim()}\n                className=\"w-full btn-primary py-4 px-6 rounded-lg font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3\"\n              >\n                {isGenerating ? (\n                  <>\n                    <i className=\"fas fa-spinner fa-spin\"></i>\n                    <span>正在生成音乐...</span>\n                  </>\n                ) : (\n                  <>\n                    <i className=\"fas fa-magic\"></i>\n                    <span>生成歌曲</span>\n                  </>\n                )}\n              </button>\n\n              {error && (\n                <div className=\"mt-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-600\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-exclamation-circle mr-2\"></i>\n                    <span className=\"font-medium\">生成失败</span>\n                  </div>\n                  <p className=\"text-sm mt-1\">{error}</p>\n                </div>\n              )}\n\n              {isGenerating && (\n                <div className=\"mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg text-blue-600\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-info-circle mr-2\"></i>\n                    <span className=\"font-medium\">生成提示</span>\n                  </div>\n                  <p className=\"text-sm mt-1\">\n                    音乐生成通常需要1-3分钟，请耐心等待...\n                  </p>\n                </div>\n              )}\n            </div>\n\n            {/* 音频播放器 */}\n            <div className=\"card\">\n              <h2 className=\"text-xl font-semibold p-6 pb-0 flex items-center\">\n                <i className=\"fas fa-play-circle mr-3 text-primary\"></i>\n                音频播放器\n              </h2>\n              <AudioPlayer\n                src={generatedAudio}\n                title=\"生成的音乐\"\n                artist=\"AI音乐创作\"\n              />\n            </div>\n\n            {/* 生成信息 */}\n            {generationInfo && (\n              <div className=\"card p-6\">\n                <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n                  <i className=\"fas fa-info-circle mr-3 text-primary\"></i>\n                  生成信息\n                </h3>\n                <div className=\"space-y-2 text-sm\">\n                  {generationInfo.duration && (\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-muted-foreground\">时长:</span>\n                      <span>{Math.round(generationInfo.duration)}秒</span>\n                    </div>\n                  )}\n                  {generationInfo.sample_rate && (\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-muted-foreground\">采样率:</span>\n                      <span>{generationInfo.sample_rate}Hz</span>\n                    </div>\n                  )}\n                  {generationInfo.format && (\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-muted-foreground\">格式:</span>\n                      <span>{generationInfo.format}</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n\n            {/* 使用说明 */}\n            <div className=\"card p-6\">\n              <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n                <i className=\"fas fa-question-circle mr-3 text-primary\"></i>\n                使用说明\n              </h3>\n              <div className=\"space-y-3 text-sm text-muted-foreground\">\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary mt-0.5\">1</span>\n                  <span>在左侧输入您的歌词内容，支持结构化标签如 [verse], [chorus] 等</span>\n                </div>\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary mt-0.5\">2</span>\n                  <span>选择音乐风格，可以上传参考音频或添加文本描述</span>\n                </div>\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary mt-0.5\">3</span>\n                  <span>点击\"生成歌曲\"按钮，等待AI为您创作音乐</span>\n                </div>\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary mt-0.5\">4</span>\n                  <span>生成完成后，您可以在音频播放器中试听和下载</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,OAAO;IACP,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,SAAS;QACT,aAAa;QACb,MAAM;IACR;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,OAAO;IACP,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,SAAS;IACT,MAAM,iBAAiB;QACrB,IAAI,CAAC,OAAO,IAAI,IAAI;YAClB,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QACT,kBAAkB;QAElB,IAAI;YACF,MAAM,SAA8B;gBAClC,OAAO;gBACP,aAAa,eAAe;gBAC5B,cAAc,aAAa;gBAC3B,OAAO;gBACP,UAAU,iBAAiB,OAAO;gBAClC,aAAa,iBAAiB,WAAW;gBACzC,OAAO,iBAAiB,IAAI;YAC9B;YAEA,MAAM,SAAS,MAAM,yHAAA,CAAA,WAAQ,CAAC,aAAa,CAAC;YAC5C,kBAAkB,OAAO,SAAS;YAClC,kBAAkB,OAAO,eAAe;QAC1C,EAAE,OAAO,KAAK;YACZ,IAAI,eAAe,yHAAA,CAAA,WAAQ,EAAE;gBAC3B,SAAS,IAAI,OAAO;YACtB,OAAO;gBACL,SAAS;YACX;YACA,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,OAAO;IACP,MAAM,cAAc;QAClB,UAAU;QACV,eAAe;QACf,aAAa;QACb,iBAAiB;QACjB,oBAAoB;YAClB,SAAS;YACT,aAAa;YACb,MAAM;QACR;QACA,kBAAkB;QAClB,kBAAkB;QAClB,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA8F;;;;;;0DAG5G,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAGjD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC;4CAAE,WAAU;;;;;;wCAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjD,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAE,WAAU;;;;;;gDAAyC;;;;;;;sDAGxD,6LAAC,uIAAA,CAAA,UAAc;4CACb,OAAO;4CACP,UAAU;4CACV,aAAY;4CACZ,WAAW;;;;;;;;;;;;8CAKf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,WAAU;kEAA0D;;;;;;kEAG5E,6LAAC;wDAAO,WAAU;kEAA6F;;;;;;kEAG/G,6LAAC;wDAAO,WAAU;kEAA6F;;;;;;;;;;;;;;;;;sDAMnH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAE,WAAU;;;;;;wDAAuC;;;;;;;8DAGtD,6LAAC;oDAAI,WAAU;8DACZ,yHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,sBACjB,6LAAC;4DAEC,SAAS,IAAM,iBAAiB;4DAChC,WAAW,CAAC,qCAAqC,EAC/C,kBAAkB,QACd,8CACA,8DACJ;sEAED,UAAU,kBAAkB,QAC5B,UAAU,sBAAsB,OAChC,UAAU,kBAAkB,OAC5B,UAAU,QAAQ,UAClB,UAAU,QAAQ,OAClB,UAAU,UAAU,OACpB,UAAU,SAAS,OACnB,UAAU,SAAS,OACnB,UAAU,SAAS,OACnB,UAAU,UAAU,OACpB,UAAU,WAAW,OACrB,UAAU,SAAS,OAAO;2DAnBtB;;;;;;;;;;;;;;;;;;;;;;8CA2Bf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAE,WAAU;;;;;;gDAAsC;;;;;;;sDAGrD,6LAAC,oIAAA,CAAA,UAAW;4CACV,cAAc;4CACd,SAAS;;;;;;;;;;;;8CAKb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAE,WAAU;;;;;;gDAAoC;;;;;;;sDAGnD,6LAAC,gJAAA,CAAA,UAAuB;4CACtB,OAAO;4CACP,UAAU;;;;;;;;;;;;8CAKd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,gBAAgB,CAAC;4CAChC,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;;;;;;wDAAmC;;;;;;;8DAGlD,6LAAC;oDAAE,WAAW,CAAC,eAAe,EAAE,eAAe,OAAO,OAAO,sBAAsB,CAAC;;;;;;;;;;;;wCAGrF,8BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;;gEAAiC;gEACxC,iBAAiB,OAAO;;;;;;;sEAElC,6LAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,MAAK;4DACL,OAAO,iBAAiB,OAAO;4DAC/B,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wEAC5C,GAAG,IAAI;wEACP,SAAS,WAAW,EAAE,MAAM,CAAC,KAAK;oEACpC,CAAC;4DACD,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;sEAAqC;;;;;;;;;;;;8DAKtD,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;;gEAAiC;gEAC3C,iBAAiB,WAAW;;;;;;;sEAEnC,6LAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,MAAK;4DACL,OAAO,iBAAiB,WAAW;4DACnC,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wEAC5C,GAAG,IAAI;wEACP,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;oEACxC,CAAC;4DACD,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;sEAAqC;;;;;;;;;;;;8DAKtD,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;;gEAAiC;gEACxC,iBAAiB,IAAI;;;;;;;sEAE/B,6LAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,MAAK;4DACL,OAAO,iBAAiB,IAAI;4DAC5B,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wEAC5C,GAAG,IAAI;wEACP,MAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oEAC/B,CAAC;4DACD,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;sEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAU9D,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,UAAU,gBAAgB,CAAC,OAAO,IAAI;4CACtC,WAAU;sDAET,6BACC;;kEACE,6LAAC;wDAAE,WAAU;;;;;;kEACb,6LAAC;kEAAK;;;;;;;6EAGR;;kEACE,6LAAC;wDAAE,WAAU;;;;;;kEACb,6LAAC;kEAAK;;;;;;;;;;;;;wCAKX,uBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAEhC,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;wCAIhC,8BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;;;;;;sEACb,6LAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAEhC,6LAAC;oDAAE,WAAU;8DAAe;;;;;;;;;;;;;;;;;;8CAQlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAE,WAAU;;;;;;gDAA2C;;;;;;;sDAG1D,6LAAC,oIAAA,CAAA,UAAW;4CACV,KAAK;4CACL,OAAM;4CACN,QAAO;;;;;;;;;;;;gCAKV,gCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAE,WAAU;;;;;;gDAA2C;;;;;;;sDAG1D,6LAAC;4CAAI,WAAU;;gDACZ,eAAe,QAAQ,kBACtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC;;gEAAM,KAAK,KAAK,CAAC,eAAe,QAAQ;gEAAE;;;;;;;;;;;;;gDAG9C,eAAe,WAAW,kBACzB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC;;gEAAM,eAAe,WAAW;gEAAC;;;;;;;;;;;;;gDAGrC,eAAe,MAAM,kBACpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC;sEAAM,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;;8CAQtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAE,WAAU;;;;;;gDAA+C;;;;;;;sDAG9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA0H;;;;;;sEAC1I,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA0H;;;;;;sEAC1I,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA0H;;;;;;sEAC1I,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA0H;;;;;;sEAC1I,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GA1YwB;KAAA", "debugId": null}}]}