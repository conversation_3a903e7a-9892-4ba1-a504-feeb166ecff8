'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import { useEffect } from 'react';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  className?: string;
}

export default function RichTextEditor({
  value,
  onChange,
  placeholder = "请输入歌词内容...",
  maxLength = 2000,
  className = ""
}: RichTextEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder,
      }),
      CharacterCount.configure({
        limit: maxLength,
      }),
    ],
    content: value,
    onUpdate: ({ editor }) => {
      // 使用 getText() 获取纯文本而不是 HTML
      onChange(editor.getText());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-invert max-w-none focus:outline-none',
      },
    },
  });

  useEffect(() => {
    if (editor && editor.getText() !== value) {
      editor.commands.setContent(value);
    }
  }, [value, editor]);

  if (!editor) {
    return null;
  }

  const characterCount = editor.storage.characterCount.characters();
  const wordCount = editor.storage.characterCount.words();

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 工具栏 */}
      <div className="flex flex-wrap gap-2 p-3 bg-muted/20 rounded-lg border border-muted/30">
        <button
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={`px-3 py-1 rounded text-sm transition-colors ${
            editor.isActive('bold')
              ? 'bg-primary text-white'
              : 'bg-muted/30 hover:bg-muted/50'
          }`}
          type="button"
        >
          <i className="fas fa-bold"></i>
        </button>
        <button
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={`px-3 py-1 rounded text-sm transition-colors ${
            editor.isActive('italic')
              ? 'bg-primary text-white'
              : 'bg-muted/30 hover:bg-muted/50'
          }`}
          type="button"
        >
          <i className="fas fa-italic"></i>
        </button>
        <button
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={`px-3 py-1 rounded text-sm transition-colors ${
            editor.isActive('strike')
              ? 'bg-primary text-white'
              : 'bg-muted/30 hover:bg-muted/50'
          }`}
          type="button"
        >
          <i className="fas fa-strikethrough"></i>
        </button>
        <div className="w-px bg-muted/30 mx-1"></div>
        <button
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={`px-3 py-1 rounded text-sm transition-colors ${
            editor.isActive('bulletList')
              ? 'bg-primary text-white'
              : 'bg-muted/30 hover:bg-muted/50'
          }`}
          type="button"
        >
          <i className="fas fa-list-ul"></i>
        </button>
        <button
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={`px-3 py-1 rounded text-sm transition-colors ${
            editor.isActive('orderedList')
              ? 'bg-primary text-white'
              : 'bg-muted/30 hover:bg-muted/50'
          }`}
          type="button"
        >
          <i className="fas fa-list-ol"></i>
        </button>
        <div className="w-px bg-muted/30 mx-1"></div>
        <button
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
          className="px-3 py-1 rounded text-sm bg-muted/30 hover:bg-muted/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          type="button"
        >
          <i className="fas fa-undo"></i>
        </button>
        <button
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
          className="px-3 py-1 rounded text-sm bg-muted/30 hover:bg-muted/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          type="button"
        >
          <i className="fas fa-redo"></i>
        </button>
      </div>

      {/* 编辑器内容 */}
      <div className="relative">
        <EditorContent editor={editor} className="min-h-[200px]" />
      </div>

      {/* 字符统计 */}
      <div className="flex justify-between items-center text-sm text-muted-foreground">
        <div className="flex gap-4">
          <span>字符: {characterCount}/{maxLength}</span>
          <span>词数: {wordCount}</span>
        </div>
        {characterCount > maxLength * 0.9 && (
          <div className="text-yellow-500">
            <i className="fas fa-exclamation-triangle mr-1"></i>
            接近字符限制
          </div>
        )}
      </div>

      {/* 歌词结构提示 */}
      <div className="p-3 bg-accent/10 rounded-lg border border-accent/20">
        <div className="text-sm text-accent-foreground">
          <i className="fas fa-info-circle mr-2"></i>
          <strong>歌词结构提示：</strong>
        </div>
        <div className="text-xs text-muted-foreground mt-1 space-y-1">
          <div>• 每段代表一个分段，以结构标签开头，以空行结尾</div>
          <div>• 每行代表一个句子，不建议使用标点符号</div>
          <div>• 分段标签：[intro], [verse], [chorus], [bridge], [outro]</div>
          <div>• [intro], [inst], [outro] 不应包含歌词</div>
          <div>• [verse], [chorus], [bridge] 需要歌词内容</div>
        </div>
      </div>
    </div>
  );
}
