import axios from 'axios';

// Gradio API 配置
const GRADIO_API_URL = 'http://************:7860';

// 音乐风格选项
export const MUSIC_GENRES = [
  'Pop',
  'R&B', 
  'Dance',
  'Jazz',
  'Folk',
  'Rock',
  'Chinese Style',
  'Chinese Tradition',
  'Metal',
  'Reggae',
  'Chinese Opera',
  'Auto'
] as const;

export type MusicGenre = typeof MUSIC_GENRES[number];

// 生成音乐的参数接口
export interface GenerateMusicParams {
  lyric: string;
  description?: string;
  prompt_audio?: File;
  genre: MusicGenre;
  cfg_coef: number;
  temperature: number;
  top_k: number;
}

// API 响应接口
export interface GenerateMusicResponse {
  audio_url: string;
  generation_info: {
    duration?: number;
    sample_rate?: number;
    channels?: number;
    format?: string;
    [key: string]: any;
  };
}

// 错误处理接口
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

class MusicApiService {
  private baseURL: string;

  constructor() {
    this.baseURL = GRADIO_API_URL;
  }

  /**
   * 生成音乐
   */
  async generateMusic(params: GenerateMusicParams): Promise<GenerateMusicResponse> {
    try {
      // 准备表单数据
      const formData = new FormData();
      
      // 添加基本参数
      formData.append('lyric', params.lyric);
      formData.append('genre', params.genre);
      formData.append('cfg_coef', params.cfg_coef.toString());
      formData.append('temperature', params.temperature.toString());
      formData.append('top_k', params.top_k.toString());
      
      // 添加可选参数
      if (params.description) {
        formData.append('description', params.description);
      }
      
      if (params.prompt_audio) {
        formData.append('prompt_audio', params.prompt_audio);
      }

      // 发送请求到 Gradio API
      const response = await axios.post(
        `${this.baseURL}/api/generate_song`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 300000, // 5分钟超时
        }
      );

      // 处理响应
      if (response.data && Array.isArray(response.data) && response.data.length >= 2) {
        const [audioPath, generationInfo] = response.data;
        
        // 构建完整的音频URL
        const audioUrl = audioPath.startsWith('http') 
          ? audioPath 
          : `${this.baseURL}/file=${audioPath}`;

        return {
          audio_url: audioUrl,
          generation_info: generationInfo || {}
        };
      } else {
        throw new Error('Invalid response format from API');
      }
    } catch (error) {
      console.error('Music generation error:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new ApiError('请求超时，音乐生成可能需要更长时间，请稍后重试');
        } else if (error.response) {
          throw new ApiError(
            `服务器错误: ${error.response.status} - ${error.response.statusText}`,
            error.response.status.toString(),
            error.response.data
          );
        } else if (error.request) {
          throw new ApiError('无法连接到音乐生成服务，请检查网络连接');
        }
      }
      
      throw new ApiError(
        error instanceof Error ? error.message : '音乐生成失败，请重试'
      );
    }
  }

  /**
   * 检查服务状态
   */
  async checkServiceStatus(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseURL}/`, {
        timeout: 10000
      });
      return response.status === 200;
    } catch (error) {
      console.error('Service status check failed:', error);
      return false;
    }
  }

  /**
   * 获取支持的音频格式
   */
  getSupportedAudioFormats(): string[] {
    return [
      'audio/mpeg',     // MP3
      'audio/wav',      // WAV
      'audio/flac',     // FLAC
      'audio/aac',      // AAC
      'audio/ogg',      // OGG
      'audio/webm',     // WebM
    ];
  }

  /**
   * 验证音频文件
   */
  validateAudioFile(file: File): { valid: boolean; error?: string } {
    const supportedFormats = this.getSupportedAudioFormats();
    
    if (!supportedFormats.includes(file.type)) {
      return {
        valid: false,
        error: '不支持的音频格式，请使用 MP3、WAV、FLAC 或 AAC 格式'
      };
    }

    // 检查文件大小 (最大 50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: '音频文件过大，请使用小于 50MB 的文件'
      };
    }

    return { valid: true };
  }

  /**
   * 获取默认生成参数
   */
  getDefaultParams(): Omit<GenerateMusicParams, 'lyric'> {
    return {
      genre: 'Pop',
      cfg_coef: 1.5,
      temperature: 0.9,
      top_k: 50,
    };
  }
}

// 导出单例实例
export const musicApi = new MusicApiService();

// 导出错误类
export class ApiError extends Error {
  public code?: string;
  public details?: any;

  constructor(message: string, code?: string, details?: any) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.details = details;
  }
}

// 工具函数：格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函数：格式化时长
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}
