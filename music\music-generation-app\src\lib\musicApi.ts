import axios from 'axios';

// Gradio API 配置
const GRADIO_API_URL = 'http://************:7860';

// 音乐风格选项
export const MUSIC_GENRES = [
  'Pop',
  'R&B', 
  'Dance',
  'Jazz',
  'Folk',
  'Rock',
  'Chinese Style',
  'Chinese Tradition',
  'Metal',
  'Reggae',
  'Chinese Opera',
  'Auto'
] as const;

export type MusicGenre = typeof MUSIC_GENRES[number];

// 生成音乐的参数接口
export interface GenerateMusicParams {
  lyric: string;
  description?: string;
  prompt_audio?: File;
  genre: MusicGenre;
  cfg_coef: number;
  temperature: number;
  top_k: number;
}

// API 响应接口
export interface GenerateMusicResponse {
  audio_url: string;
  generation_info: {
    duration?: number;
    sample_rate?: number;
    channels?: number;
    format?: string;
    [key: string]: any;
  };
}

// 错误处理接口
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

class MusicApiService {
  private baseURL: string;

  constructor() {
    this.baseURL = GRADIO_API_URL;
  }

  /**
   * 生成音乐
   */
  async generateMusic(params: GenerateMusicParams): Promise<GenerateMusicResponse> {
    try {
      // 确保歌词格式正确 - 按照后端期望的格式处理
      let formattedLyric = params.lyric.trim();

      // 按照原始app.py的格式处理逻辑
      // 1. 替换标签格式
      formattedLyric = formattedLyric
        .replace(/\[intro\]/g, "[intro-short]")
        .replace(/\[inst\]/g, "[inst-short]")
        .replace(/\[outro\]/g, "[outro-short]");

      // 2. 按段落分割（空行分隔）
      const paragraphs = formattedLyric.split('\n\n').filter(p => p.trim());
      const processedParagraphs: string[] = [];

      for (const para of paragraphs) {
        const lines = para.split('\n').map(line => line.trim()).filter(line => line);
        if (lines.length === 0) continue;

        // 检查第一行是否是结构标签
        const firstLine = lines[0];
        let structTag = '';
        let lyricsLines: string[] = [];

        // 如果第一行包含结构标签，提取标签和歌词
        const tagMatch = firstLine.match(/^(\[[\w-]+\])\s*(.*)/);
        if (tagMatch) {
          structTag = tagMatch[1];
          const restOfFirstLine = tagMatch[2].trim();
          lyricsLines = restOfFirstLine ? [restOfFirstLine, ...lines.slice(1)] : lines.slice(1);
        } else {
          // 如果没有标签，默认为verse
          structTag = '[verse]';
          lyricsLines = lines;
        }

        // 检查是否是有效的结构标签
        const validTags = ['[verse]', '[chorus]', '[bridge]', '[intro-short]', '[intro-medium]', '[intro-long]', '[outro-short]', '[outro-medium]', '[outro-long]', '[inst-short]', '[inst-medium]', '[inst-long]', '[silence]'];

        if (!validTags.includes(structTag.toLowerCase())) {
          // 如果不是有效标签，默认为verse
          structTag = '[verse]';
          lyricsLines = lines;
        }

        // 处理有效的结构标签
        const vocalStructs = ['[verse]', '[chorus]', '[bridge]'];
        if (vocalStructs.includes(structTag.toLowerCase())) {
          // 需要歌词的段落
          if (lyricsLines.length === 0 || lyricsLines.every(line => !line.trim())) {
            throw new Error(`${structTag} 段落需要包含歌词内容`);
          }
          // 清理歌词内容，移除特殊字符（按照app.py的逻辑）
          const cleanedLyrics = lyricsLines
            .map(line => line.replace(/[^\w\s\[\]\-\u4e00-\u9fff\u3040-\u309f\u30a0-\u30ff\uac00-\ud7af\u00c0-\u017f]/g, ""))
            .filter(line => line.trim())
            .join('.');
          processedParagraphs.push(`${structTag} ${cleanedLyrics}`);
        } else {
          // 不需要歌词的段落（intro, inst, outro等）
          if (lyricsLines.length > 0 && lyricsLines.some(line => line.trim())) {
            throw new Error(`${structTag} 段落不应包含歌词内容`);
          }
          processedParagraphs.push(structTag);
        }
      }

      // 检查是否至少包含一个vocal段落
      const hasVocalStruct = processedParagraphs.some(para =>
        para.startsWith('[verse]') || para.startsWith('[chorus]') || para.startsWith('[bridge]')
      );
      if (!hasVocalStruct) {
        throw new Error('歌词必须至少包含以下结构之一: [verse], [chorus], [bridge]');
      }

      // 3. 用分号连接段落（按照app.py的格式）
      formattedLyric = processedParagraphs.join(' ; ');

      // 准备Gradio API调用数据 - 确保所有数值参数都是正确的类型
      const requestData = {
        data: [
          formattedLyric,                           // lyric (string)
          params.description || null,               // description (string or null)
          params.prompt_audio || null,              // prompt_audio (file or null)
          params.genre || "Pop",                    // genre (string)
          parseFloat(String(params.cfg_coef || 1.5)),      // cfg_coef (float)
          parseFloat(String(params.temperature || 0.9)),   // temperature (float)
          parseInt(String(params.top_k || 50), 10)         // top_k (integer)
        ],
        fn_index: 0  // 指定函数索引
      };

      console.log('Sending request to Gradio API:', requestData);
      console.log('Formatted lyric:', JSON.stringify(formattedLyric));

      // 使用正确的Gradio API端点
      const response = await axios.post(
        `${this.baseURL}/api/predict`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 300000, // 5分钟超时
        }
      );

      console.log('Gradio API response:', response.data);

      // 处理响应
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        const responseData = response.data.data;

        if (responseData.length >= 1) {
          const audioData = responseData[0];
          const generationInfo = responseData[1] || {};

          if (audioData === null) {
            // 生成失败
            const errorMsg = typeof generationInfo === 'string' ? generationInfo : '音乐生成失败';
            throw new ApiError(errorMsg);
          }

          // 处理音频数据
          let audioUrl = '';
          if (audioData && Array.isArray(audioData) && audioData.length === 2) {
            // audioData 格式: [sample_rate, audio_array]
            const [sampleRate, audioArray] = audioData;

            // 创建音频blob
            const audioBlob = this.createAudioBlob(audioArray, sampleRate);
            audioUrl = URL.createObjectURL(audioBlob);
          } else if (typeof audioData === 'string') {
            // 如果返回的是文件路径
            audioUrl = audioData.startsWith('http')
              ? audioData
              : `${this.baseURL}/file=${audioData}`;
          }

          return {
            audio_url: audioUrl,
            generation_info: generationInfo || {}
          };
        }
      }

      throw new Error('Invalid response format from API');
    } catch (error) {
      console.error('Music generation error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new ApiError('请求超时，音乐生成可能需要更长时间，请稍后重试');
        } else if (error.response) {
          const errorMsg = error.response.data?.error || error.response.statusText;
          throw new ApiError(
            `服务器错误 (${error.response.status}): ${errorMsg}`,
            error.response.status.toString(),
            error.response.data
          );
        } else if (error.request) {
          throw new ApiError('无法连接到音乐生成服务，请检查网络连接');
        }
      }

      throw new ApiError(
        error instanceof Error ? error.message : '音乐生成失败，请重试'
      );
    }
  }

  /**
   * 创建音频Blob对象
   */
  private createAudioBlob(audioArray: number[][], sampleRate: number): Blob {
    // 将音频数组转换为WAV格式
    const length = audioArray[0].length;
    const numberOfChannels = audioArray.length;
    const buffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
    const view = new DataView(buffer);

    // WAV文件头
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * numberOfChannels * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numberOfChannels * 2, true);
    view.setUint16(32, numberOfChannels * 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * numberOfChannels * 2, true);

    // 音频数据
    let offset = 44;
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, audioArray[channel][i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
    }

    return new Blob([buffer], { type: 'audio/wav' });
  }

  /**
   * 检查服务状态
   */
  async checkServiceStatus(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseURL}/`, {
        timeout: 10000
      });
      return response.status === 200;
    } catch (error) {
      console.error('Service status check failed:', error);
      return false;
    }
  }

  /**
   * 获取支持的音频格式
   */
  getSupportedAudioFormats(): string[] {
    return [
      'audio/mpeg',     // MP3
      'audio/wav',      // WAV
      'audio/flac',     // FLAC
      'audio/aac',      // AAC
      'audio/ogg',      // OGG
      'audio/webm',     // WebM
    ];
  }

  /**
   * 验证音频文件
   */
  validateAudioFile(file: File): { valid: boolean; error?: string } {
    const supportedFormats = this.getSupportedAudioFormats();
    
    if (!supportedFormats.includes(file.type)) {
      return {
        valid: false,
        error: '不支持的音频格式，请使用 MP3、WAV、FLAC 或 AAC 格式'
      };
    }

    // 检查文件大小 (最大 50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: '音频文件过大，请使用小于 50MB 的文件'
      };
    }

    return { valid: true };
  }

  /**
   * 获取默认生成参数
   */
  getDefaultParams(): Omit<GenerateMusicParams, 'lyric'> {
    return {
      genre: 'Pop',
      cfg_coef: 1.5,
      temperature: 0.9,
      top_k: 50,
    };
  }
}

// 导出单例实例
export const musicApi = new MusicApiService();

// 导出错误类
export class ApiError extends Error {
  public code?: string;
  public details?: any;

  constructor(message: string, code?: string, details?: any) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.details = details;
  }
}

// 工具函数：格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函数：格式化时长
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}
