import axios from 'axios';

// Gradio API 配置
const GRADIO_API_URL = 'http://************:7860';

// 音乐风格选项
export const MUSIC_GENRES = [
  'Pop',
  'R&B', 
  'Dance',
  'Jazz',
  'Folk',
  'Rock',
  'Chinese Style',
  'Chinese Tradition',
  'Metal',
  'Reggae',
  'Chinese Opera',
  'Auto'
] as const;

export type MusicGenre = typeof MUSIC_GENRES[number];

// 生成音乐的参数接口
export interface GenerateMusicParams {
  lyric: string;
  description?: string;
  prompt_audio?: File;
  genre: MusicGenre;
  cfg_coef: number;
  temperature: number;
  top_k: number;
}

// API 响应接口
export interface GenerateMusicResponse {
  audio_url: string;
  generation_info: {
    duration?: number;
    sample_rate?: number;
    channels?: number;
    format?: string;
    [key: string]: any;
  };
}

// 错误处理接口
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

class MusicApiService {
  private baseURL: string;

  constructor() {
    this.baseURL = GRADIO_API_URL;
  }

  /**
   * 生成音乐
   */
  async generateMusic(params: GenerateMusicParams): Promise<GenerateMusicResponse> {
    try {
      // 确保歌词格式正确 - 使用正确的结构标签
      let formattedLyric = params.lyric.trim();

      // 处理歌词格式，确保每个段落都以正确的标签开头
      const lines = formattedLyric.split('\n');
      const processedLines: string[] = [];

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // 跳过空行
        if (line === '') {
          continue;
        }

        // 如果是结构标签行，直接添加
        if (line.startsWith('[') && line.endsWith(']')) {
          processedLines.push(line);
        } else {
          // 如果是歌词内容，确保前面有结构标签
          if (processedLines.length === 0 || !processedLines[processedLines.length - 1].startsWith('[')) {
            // 如果没有前置标签，添加默认的verse标签
            processedLines.push('[verse]');
          }
          processedLines.push(line);
        }
      }

      formattedLyric = processedLines.join('\n');

      // 准备Gradio API调用数据 - 使用正确的函数名
      const requestData = {
        data: [
          formattedLyric,                  // lyric
          params.description || null,      // description (null instead of empty string)
          params.prompt_audio || null,     // prompt_audio
          params.genre || "Pop",           // genre (default to Pop)
          params.cfg_coef || 1.5,          // cfg_coef
          params.temperature || 0.9,       // temperature
          params.top_k || 50               // top_k
        ],
        fn_index: 0  // 指定函数索引
      };

      console.log('Sending request to Gradio API:', requestData);

      // 使用正确的Gradio API端点
      const response = await axios.post(
        `${this.baseURL}/api/predict`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 300000, // 5分钟超时
        }
      );

      console.log('Gradio API response:', response.data);

      // 处理响应
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        const responseData = response.data.data;

        if (responseData.length >= 1) {
          const audioData = responseData[0];
          const generationInfo = responseData[1] || {};

          if (audioData === null) {
            // 生成失败
            const errorMsg = typeof generationInfo === 'string' ? generationInfo : '音乐生成失败';
            throw new ApiError(errorMsg);
          }

          // 处理音频数据
          let audioUrl = '';
          if (audioData && Array.isArray(audioData) && audioData.length === 2) {
            // audioData 格式: [sample_rate, audio_array]
            const [sampleRate, audioArray] = audioData;

            // 创建音频blob
            const audioBlob = this.createAudioBlob(audioArray, sampleRate);
            audioUrl = URL.createObjectURL(audioBlob);
          } else if (typeof audioData === 'string') {
            // 如果返回的是文件路径
            audioUrl = audioData.startsWith('http')
              ? audioData
              : `${this.baseURL}/file=${audioData}`;
          }

          return {
            audio_url: audioUrl,
            generation_info: generationInfo || {}
          };
        }
      }

      throw new Error('Invalid response format from API');
    } catch (error) {
      console.error('Music generation error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new ApiError('请求超时，音乐生成可能需要更长时间，请稍后重试');
        } else if (error.response) {
          const errorMsg = error.response.data?.error || error.response.statusText;
          throw new ApiError(
            `服务器错误 (${error.response.status}): ${errorMsg}`,
            error.response.status.toString(),
            error.response.data
          );
        } else if (error.request) {
          throw new ApiError('无法连接到音乐生成服务，请检查网络连接');
        }
      }

      throw new ApiError(
        error instanceof Error ? error.message : '音乐生成失败，请重试'
      );
    }
  }

  /**
   * 创建音频Blob对象
   */
  private createAudioBlob(audioArray: number[][], sampleRate: number): Blob {
    // 将音频数组转换为WAV格式
    const length = audioArray[0].length;
    const numberOfChannels = audioArray.length;
    const buffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
    const view = new DataView(buffer);

    // WAV文件头
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * numberOfChannels * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numberOfChannels * 2, true);
    view.setUint16(32, numberOfChannels * 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * numberOfChannels * 2, true);

    // 音频数据
    let offset = 44;
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, audioArray[channel][i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
    }

    return new Blob([buffer], { type: 'audio/wav' });
  }

  /**
   * 检查服务状态
   */
  async checkServiceStatus(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseURL}/`, {
        timeout: 10000
      });
      return response.status === 200;
    } catch (error) {
      console.error('Service status check failed:', error);
      return false;
    }
  }

  /**
   * 获取支持的音频格式
   */
  getSupportedAudioFormats(): string[] {
    return [
      'audio/mpeg',     // MP3
      'audio/wav',      // WAV
      'audio/flac',     // FLAC
      'audio/aac',      // AAC
      'audio/ogg',      // OGG
      'audio/webm',     // WebM
    ];
  }

  /**
   * 验证音频文件
   */
  validateAudioFile(file: File): { valid: boolean; error?: string } {
    const supportedFormats = this.getSupportedAudioFormats();
    
    if (!supportedFormats.includes(file.type)) {
      return {
        valid: false,
        error: '不支持的音频格式，请使用 MP3、WAV、FLAC 或 AAC 格式'
      };
    }

    // 检查文件大小 (最大 50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: '音频文件过大，请使用小于 50MB 的文件'
      };
    }

    return { valid: true };
  }

  /**
   * 获取默认生成参数
   */
  getDefaultParams(): Omit<GenerateMusicParams, 'lyric'> {
    return {
      genre: 'Pop',
      cfg_coef: 1.5,
      temperature: 0.9,
      top_k: 50,
    };
  }
}

// 导出单例实例
export const musicApi = new MusicApiService();

// 导出错误类
export class ApiError extends Error {
  public code?: string;
  public details?: any;

  constructor(message: string, code?: string, details?: any) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.details = details;
  }
}

// 工具函数：格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函数：格式化时长
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}
