import axios from 'axios';

// Gradio API 配置
const GRADIO_API_URL = 'http://************:7860';

// 音乐风格选项
export const MUSIC_GENRES = [
  'Pop',
  'R&B', 
  'Dance',
  'Jazz',
  'Folk',
  'Rock',
  'Chinese Style',
  'Chinese Tradition',
  'Metal',
  'Reggae',
  'Chinese Opera',
  'Auto'
] as const;

export type MusicGenre = typeof MUSIC_GENRES[number];

// 生成音乐的参数接口
export interface GenerateMusicParams {
  lyric: string;
  description?: string;
  prompt_audio?: File;
  genre: MusicGenre;
  cfg_coef: number;
  temperature: number;
  top_k: number;
}

// API 响应接口
export interface GenerateMusicResponse {
  audio_url: string;
  generation_info: {
    duration?: number;
    sample_rate?: number;
    channels?: number;
    format?: string;
    [key: string]: any;
  };
}

// 错误处理接口
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

class MusicApiService {
  private baseURL: string;

  constructor() {
    this.baseURL = GRADIO_API_URL;
  }

  /**
   * 生成音乐
   */
  async generateMusic(params: GenerateMusicParams): Promise<GenerateMusicResponse> {
    try {
      // 确保歌词格式正确
      let formattedLyric = params.lyric;
      if (!formattedLyric.includes('[verse]') && !formattedLyric.includes('[chorus]')) {
        // 如果歌词没有标签，添加基本结构
        formattedLyric = `[verse]\n${formattedLyric}`;
      }

      // 准备Gradio API调用数据
      const requestData = {
        data: [
          formattedLyric,                  // lyric
          params.description || "",        // description
          params.prompt_audio || null,     // prompt_audio
          params.genre,                    // genre
          params.cfg_coef,                 // cfg_coef
          params.temperature,              // temperature
          params.top_k                     // top_k
        ]
      };

      console.log('Sending request to Gradio API:', requestData);

      // 第一步：发起生成请求
      const initResponse = await axios.post(
        `${this.baseURL}/call/generate_song`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 10000, // 初始请求10秒超时
        }
      );

      console.log('Gradio API init response:', initResponse.data);

      if (!initResponse.data?.event_id) {
        throw new Error('Failed to get event_id from API');
      }

      const eventId = initResponse.data.event_id;

      // 第二步：轮询获取结果
      const maxAttempts = 60; // 最多轮询60次（5分钟）
      let attempts = 0;

      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 5000)); // 等待5秒
        attempts++;

        try {
          const resultResponse = await axios.get(
            `${this.baseURL}/call/generate_song/${eventId}`,
            {
              timeout: 10000,
            }
          );

          console.log(`Polling attempt ${attempts}:`, resultResponse.data);

          // 解析Server-Sent Events格式的响应
          const content = resultResponse.data;
          if (typeof content === 'string' && content.includes('event: complete')) {
            // 提取data部分
            const dataMatch = content.match(/data: (.+)/);
            if (dataMatch) {
              try {
                const data = JSON.parse(dataMatch[1]);
                if (Array.isArray(data) && data.length >= 1) {
                  const audioPath = data[0];
                  const generationInfo = data[1] || {};

                  if (audioPath === null) {
                    // 生成失败
                    const errorMsg = typeof generationInfo === 'string' ? generationInfo : '音乐生成失败';
                    throw new ApiError(errorMsg);
                  }

                  // 构建完整的音频URL
                  let audioUrl = '';
                  if (typeof audioPath === 'string') {
                    audioUrl = audioPath.startsWith('http')
                      ? audioPath
                      : `${this.baseURL}/file=${audioPath}`;
                  } else if (audioPath && audioPath.path) {
                    audioUrl = `${this.baseURL}/file=${audioPath.path}`;
                  }

                  return {
                    audio_url: audioUrl,
                    generation_info: generationInfo || {}
                  };
                }
              } catch (parseError) {
                console.error('Failed to parse result data:', parseError);
              }
            }
          }
        } catch (pollError) {
          console.log(`Polling attempt ${attempts} failed:`, pollError);
          // 继续轮询
        }
      }

      throw new ApiError('音乐生成超时，请稍后重试');
    } catch (error) {
      console.error('Music generation error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new ApiError('请求超时，音乐生成可能需要更长时间，请稍后重试');
        } else if (error.response) {
          const errorMsg = error.response.data?.error || error.response.statusText;
          throw new ApiError(
            `服务器错误 (${error.response.status}): ${errorMsg}`,
            error.response.status.toString(),
            error.response.data
          );
        } else if (error.request) {
          throw new ApiError('无法连接到音乐生成服务，请检查网络连接');
        }
      }

      throw new ApiError(
        error instanceof Error ? error.message : '音乐生成失败，请重试'
      );
    }
  }

  /**
   * 检查服务状态
   */
  async checkServiceStatus(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseURL}/`, {
        timeout: 10000
      });
      return response.status === 200;
    } catch (error) {
      console.error('Service status check failed:', error);
      return false;
    }
  }

  /**
   * 获取支持的音频格式
   */
  getSupportedAudioFormats(): string[] {
    return [
      'audio/mpeg',     // MP3
      'audio/wav',      // WAV
      'audio/flac',     // FLAC
      'audio/aac',      // AAC
      'audio/ogg',      // OGG
      'audio/webm',     // WebM
    ];
  }

  /**
   * 验证音频文件
   */
  validateAudioFile(file: File): { valid: boolean; error?: string } {
    const supportedFormats = this.getSupportedAudioFormats();
    
    if (!supportedFormats.includes(file.type)) {
      return {
        valid: false,
        error: '不支持的音频格式，请使用 MP3、WAV、FLAC 或 AAC 格式'
      };
    }

    // 检查文件大小 (最大 50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: '音频文件过大，请使用小于 50MB 的文件'
      };
    }

    return { valid: true };
  }

  /**
   * 获取默认生成参数
   */
  getDefaultParams(): Omit<GenerateMusicParams, 'lyric'> {
    return {
      genre: 'Pop',
      cfg_coef: 1.5,
      temperature: 0.9,
      top_k: 50,
    };
  }
}

// 导出单例实例
export const musicApi = new MusicApiService();

// 导出错误类
export class ApiError extends Error {
  public code?: string;
  public details?: any;

  constructor(message: string, code?: string, details?: any) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.details = details;
  }
}

// 工具函数：格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函数：格式化时长
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}
