'use client';

import { useState } from 'react';

interface SongDescriptionSelectorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

// 预设的歌曲描述选项
const PRESET_DESCRIPTIONS = {
  gender: {
    title: '性别',
    icon: 'fas fa-user',
    options: [
      { label: '男声', value: 'male vocals', description: '男性歌手演唱' },
      { label: '女声', value: 'female vocals', description: '女性歌手演唱' },
      { label: '混合', value: 'mixed vocals', description: '男女混合演唱' },
      { label: '童声', value: 'children vocals', description: '儿童歌手演唱' },
    ]
  },
  mood: {
    title: '情感',
    icon: 'fas fa-heart',
    options: [
      { label: '快乐', value: 'happy, upbeat, joyful', description: '欢快愉悦的情感' },
      { label: '悲伤', value: 'sad, melancholic, emotional', description: '悲伤忧郁的情感' },
      { label: '浪漫', value: 'romantic, love, tender', description: '浪漫温柔的情感' },
      { label: '激昂', value: 'energetic, powerful, passionate', description: '激情澎湃的情感' },
      { label: '平静', value: 'calm, peaceful, serene', description: '平静安详的情感' },
      { label: '神秘', value: 'mysterious, dark, atmospheric', description: '神秘深邃的情感' },
    ]
  },
  style: {
    title: '风格',
    icon: 'fas fa-music',
    options: [
      { label: '流行', value: 'pop, mainstream, catchy', description: '流行音乐风格' },
      { label: '摇滚', value: 'rock, electric guitar, drums', description: '摇滚音乐风格' },
      { label: '民谣', value: 'folk, acoustic, storytelling', description: '民谣音乐风格' },
      { label: '电子', value: 'electronic, synthesizer, digital', description: '电子音乐风格' },
      { label: '爵士', value: 'jazz, swing, improvisation', description: '爵士音乐风格' },
      { label: '古典', value: 'classical, orchestral, elegant', description: '古典音乐风格' },
      { label: '中国风', value: 'chinese traditional, oriental, cultural', description: '中国传统风格' },
    ]
  },
  tempo: {
    title: '节奏',
    icon: 'fas fa-tachometer-alt',
    options: [
      { label: '慢板', value: 'slow tempo, 60-80 BPM', description: '缓慢的节奏' },
      { label: '中板', value: 'medium tempo, 90-120 BPM', description: '中等的节奏' },
      { label: '快板', value: 'fast tempo, 130-160 BPM', description: '快速的节奏' },
      { label: '极快', value: 'very fast tempo, 170+ BPM', description: '非常快的节奏' },
    ]
  },
  instruments: {
    title: '乐器',
    icon: 'fas fa-guitar',
    options: [
      { label: '钢琴', value: 'piano, keys', description: '钢琴演奏' },
      { label: '吉他', value: 'guitar, acoustic guitar', description: '吉他演奏' },
      { label: '小提琴', value: 'violin, strings', description: '小提琴演奏' },
      { label: '鼓组', value: 'drums, percussion', description: '鼓组节奏' },
      { label: '贝斯', value: 'bass, bass guitar', description: '贝斯低音' },
      { label: '合成器', value: 'synthesizer, electronic', description: '合成器音色' },
      { label: '管弦乐', value: 'orchestra, symphonic', description: '管弦乐团' },
      { label: '中国乐器', value: 'chinese instruments, erhu, guzheng', description: '中国传统乐器' },
    ]
  }
};

export default function SongDescriptionSelector({
  value,
  onChange,
  className = ""
}: SongDescriptionSelectorProps) {
  const [selectedPresets, setSelectedPresets] = useState<string[]>([]);

  const handlePresetToggle = (presetValue: string) => {
    const newPresets = selectedPresets.includes(presetValue)
      ? selectedPresets.filter(p => p !== presetValue)
      : [...selectedPresets, presetValue];
    
    setSelectedPresets(newPresets);
    
    // 更新描述文本
    const newDescription = newPresets.join(', ');
    onChange(newDescription);
  };

  const handleTextChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(event.target.value);
    // 清空预设选择状态，因为用户手动编辑了
    setSelectedPresets([]);
  };

  const insertPreset = (presetValue: string) => {
    const newValue = value ? `${value}, ${presetValue}` : presetValue;
    onChange(newValue);
  };

  const clearAll = () => {
    onChange('');
    setSelectedPresets([]);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 预设选项 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold flex items-center">
            <i className="fas fa-palette mr-2 text-primary"></i>
            预设描述选项
          </h3>
          <button
            onClick={clearAll}
            className="text-sm text-muted-foreground hover:text-foreground transition-colors"
          >
            <i className="fas fa-eraser mr-1"></i>
            清空所有
          </button>
        </div>

        {Object.entries(PRESET_DESCRIPTIONS).map(([key, category]) => (
          <div key={key} className="space-y-2">
            <h4 className="text-sm font-medium text-muted-foreground flex items-center">
              <i className={`${category.icon} mr-2`}></i>
              {category.title}
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {category.options.map((option) => (
                <button
                  key={option.value}
                  onClick={() => insertPreset(option.value)}
                  className="p-3 text-left bg-muted/20 hover:bg-muted/40 rounded-lg border border-muted/30 hover:border-primary/50 transition-all duration-200 group"
                  title={option.description}
                >
                  <div className="text-sm font-medium group-hover:text-primary transition-colors">
                    {option.label}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {option.description}
                  </div>
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* 自定义文本输入 */}
      <div className="space-y-3">
        <label className="text-sm font-medium flex items-center">
          <i className="fas fa-edit mr-2 text-primary"></i>
          自定义描述 (英文)
        </label>
        <textarea
          value={value}
          onChange={handleTextChange}
          placeholder="描述歌曲的性别、音色、风格、情感、乐器和BPM。例如：female vocals, pop, happy, piano, 120 BPM"
          className="w-full h-24 p-3 bg-muted/20 border border-muted/30 rounded-lg focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all resize-none"
        />
        <div className="text-xs text-muted-foreground">
          字符数: {value.length} • 建议使用英文描述以获得更好的效果
        </div>
      </div>

      {/* 使用提示 */}
      <div className="p-4 bg-accent/10 rounded-lg border border-accent/20">
        <div className="text-sm text-accent-foreground">
          <i className="fas fa-lightbulb mr-2"></i>
          <strong>使用提示：</strong>
        </div>
        <div className="text-xs text-muted-foreground mt-2 space-y-1">
          <div>• 点击预设选项可快速插入常用描述</div>
          <div>• 可以组合多个预设选项创建复合描述</div>
          <div>• 支持手动编辑和自定义描述内容</div>
          <div>• 建议包含：性别、风格、情感、主要乐器、节奏等信息</div>
          <div>• 使用英文描述可以获得更准确的生成效果</div>
        </div>
      </div>
    </div>
  );
}
