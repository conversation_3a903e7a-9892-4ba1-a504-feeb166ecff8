'use client';

import { useState } from 'react';
import RichTextEditor from '@/components/RichTextEditor';
import AudioUpload from '@/components/AudioUpload';
import SongDescriptionSelector from '@/components/SongDescriptionSelector';
import AudioPlayer from '@/components/AudioPlayer';
import { musicApi, MUSIC_GENRES, type MusicGenre, type GenerateMusicParams, ApiError } from '@/lib/musicApi';

export default function Home() {
  // 表单状态
  const [lyrics, setLyrics] = useState('');
  const [description, setDescription] = useState('');
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [selectedGenre, setSelectedGenre] = useState<MusicGenre>('Pop');
  const [advancedSettings, setAdvancedSettings] = useState({
    cfgCoef: 1.5,
    temperature: 0.9,
    topK: 50,
  });
  const [showAdvanced, setShowAdvanced] = useState(false);

  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedAudio, setGeneratedAudio] = useState<string>('');
  const [generationInfo, setGenerationInfo] = useState<any>(null);
  const [error, setError] = useState<string>('');

  // 处理音乐生成
  const handleGenerate = async () => {
    if (!lyrics.trim()) {
      setError('请输入歌词内容');
      return;
    }

    setIsGenerating(true);
    setError('');
    setGeneratedAudio('');

    try {
      const params: GenerateMusicParams = {
        lyric: lyrics,
        description: description || undefined,
        prompt_audio: audioFile || undefined,
        genre: selectedGenre,
        cfg_coef: advancedSettings.cfgCoef,
        temperature: advancedSettings.temperature,
        top_k: advancedSettings.topK,
      };

      const result = await musicApi.generateMusic(params);
      setGeneratedAudio(result.audio_url);
      setGenerationInfo(result.generation_info);
    } catch (err) {
      if (err instanceof ApiError) {
        setError(err.message);
      } else {
        setError('音乐生成失败，请重试');
      }
      console.error('Generation error:', err);
    } finally {
      setIsGenerating(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    setLyrics('');
    setDescription('');
    setAudioFile(null);
    setSelectedGenre('Pop');
    setAdvancedSettings({
      cfgCoef: 1.5,
      temperature: 0.9,
      topK: 50,
    });
    setGeneratedAudio('');
    setGenerationInfo(null);
    setError('');
  };

  return (
    <div className="min-h-screen bg-background">
      {/* 头部 */}
      <header className="border-b border-muted/20 bg-background/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-primary rounded-lg">
                <i className="fas fa-music text-white text-lg"></i>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                  AI音乐创作平台
                </h1>
                <p className="text-sm text-muted-foreground">基于人工智能的音乐生成服务</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleReset}
                className="px-4 py-2 text-sm bg-muted/20 hover:bg-muted/40 rounded-lg transition-colors"
              >
                <i className="fas fa-refresh mr-2"></i>
                重置
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：输入区域 */}
          <div className="space-y-6">
            {/* 歌词输入 */}
            <div className="card p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <i className="fas fa-pen-fancy mr-3 text-primary"></i>
                歌词创作
              </h2>
              <RichTextEditor
                value={lyrics}
                onChange={setLyrics}
                placeholder="请输入您的歌词内容..."
                maxLength={2000}
              />
            </div>

            {/* 选项卡 */}
            <div className="card">
              <div className="border-b border-muted/20">
                <nav className="flex space-x-8 px-6">
                  <button className="py-4 border-b-2 border-primary text-primary font-medium">
                    风格选择
                  </button>
                  <button className="py-4 border-b-2 border-transparent text-muted-foreground hover:text-foreground font-medium">
                    音频提示
                  </button>
                  <button className="py-4 border-b-2 border-transparent text-muted-foreground hover:text-foreground font-medium">
                    文本提示
                  </button>
                </nav>
              </div>

              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <i className="fas fa-palette mr-3 text-primary"></i>
                  音乐风格
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {MUSIC_GENRES.map((genre) => (
                    <button
                      key={genre}
                      onClick={() => setSelectedGenre(genre)}
                      className={`p-3 rounded-lg border transition-all ${
                        selectedGenre === genre
                          ? 'border-primary bg-primary/10 text-primary'
                          : 'border-muted/30 hover:border-primary/50 hover:bg-primary/5'
                      }`}
                    >
                      {genre === 'Chinese Style' ? '中国风' :
                       genre === 'Chinese Tradition' ? '传统' :
                       genre === 'Chinese Opera' ? '戏曲' :
                       genre === 'R&B' ? '节奏布鲁斯' :
                       genre === 'Pop' ? '流行' :
                       genre === 'Dance' ? '舞曲' :
                       genre === 'Jazz' ? '爵士' :
                       genre === 'Folk' ? '民谣' :
                       genre === 'Rock' ? '摇滚' :
                       genre === 'Metal' ? '金属' :
                       genre === 'Reggae' ? '雷鬼' :
                       genre === 'Auto' ? '自动' : genre}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* 音频上传 */}
            <div className="card p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <i className="fas fa-upload mr-3 text-primary"></i>
                音频提示 (可选)
              </h2>
              <AudioUpload
                onFileSelect={setAudioFile}
                maxSize={50}
              />
            </div>

            {/* 歌曲描述 */}
            <div className="card p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <i className="fas fa-edit mr-3 text-primary"></i>
                歌曲描述 (可选)
              </h2>
              <SongDescriptionSelector
                value={description}
                onChange={setDescription}
              />
            </div>

            {/* 高级设置 */}
            <div className="card p-6">
              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="w-full flex items-center justify-between text-lg font-semibold mb-4"
              >
                <div className="flex items-center">
                  <i className="fas fa-cog mr-3 text-primary"></i>
                  高级设置
                </div>
                <i className={`fas fa-chevron-${showAdvanced ? 'up' : 'down'} text-muted-foreground`}></i>
              </button>

              {showAdvanced && (
                <div className="space-y-4 pt-4 border-t border-muted/20">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      CFG系数: {advancedSettings.cfgCoef}
                    </label>
                    <input
                      type="range"
                      min="1"
                      max="3"
                      step="0.1"
                      value={advancedSettings.cfgCoef}
                      onChange={(e) => setAdvancedSettings(prev => ({
                        ...prev,
                        cfgCoef: parseFloat(e.target.value)
                      }))}
                      className="w-full h-2 bg-muted/30 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="text-xs text-muted-foreground mt-1">
                      控制生成结果与提示的匹配程度
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      温度: {advancedSettings.temperature}
                    </label>
                    <input
                      type="range"
                      min="0.1"
                      max="1.5"
                      step="0.1"
                      value={advancedSettings.temperature}
                      onChange={(e) => setAdvancedSettings(prev => ({
                        ...prev,
                        temperature: parseFloat(e.target.value)
                      }))}
                      className="w-full h-2 bg-muted/30 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="text-xs text-muted-foreground mt-1">
                      控制生成结果的随机性和创造性
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Top-K: {advancedSettings.topK}
                    </label>
                    <input
                      type="range"
                      min="10"
                      max="100"
                      step="10"
                      value={advancedSettings.topK}
                      onChange={(e) => setAdvancedSettings(prev => ({
                        ...prev,
                        topK: parseInt(e.target.value)
                      }))}
                      className="w-full h-2 bg-muted/30 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="text-xs text-muted-foreground mt-1">
                      限制候选词汇的数量，影响生成质量
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 右侧：播放器和控制 */}
          <div className="space-y-6">
            {/* 生成按钮 */}
            <div className="card p-6">
              <button
                onClick={handleGenerate}
                disabled={isGenerating || !lyrics.trim()}
                className="w-full btn-primary py-4 px-6 rounded-lg font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3"
              >
                {isGenerating ? (
                  <>
                    <i className="fas fa-spinner fa-spin"></i>
                    <span>正在生成音乐...</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-magic"></i>
                    <span>生成歌曲</span>
                  </>
                )}
              </button>

              {error && (
                <div className="mt-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-600">
                  <div className="flex items-center">
                    <i className="fas fa-exclamation-circle mr-2"></i>
                    <span className="font-medium">生成失败</span>
                  </div>
                  <p className="text-sm mt-1">{error}</p>
                </div>
              )}

              {isGenerating && (
                <div className="mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg text-blue-600">
                  <div className="flex items-center">
                    <i className="fas fa-info-circle mr-2"></i>
                    <span className="font-medium">生成提示</span>
                  </div>
                  <p className="text-sm mt-1">
                    音乐生成通常需要1-3分钟，请耐心等待...
                  </p>
                </div>
              )}
            </div>

            {/* 音频播放器 */}
            <div className="card">
              <h2 className="text-xl font-semibold p-6 pb-0 flex items-center">
                <i className="fas fa-play-circle mr-3 text-primary"></i>
                音频播放器
              </h2>
              <AudioPlayer
                src={generatedAudio}
                title="生成的音乐"
                artist="AI音乐创作"
              />
            </div>

            {/* 生成信息 */}
            {generationInfo && (
              <div className="card p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <i className="fas fa-info-circle mr-3 text-primary"></i>
                  生成信息
                </h3>
                <div className="space-y-2 text-sm">
                  {generationInfo.duration && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">时长:</span>
                      <span>{Math.round(generationInfo.duration)}秒</span>
                    </div>
                  )}
                  {generationInfo.sample_rate && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">采样率:</span>
                      <span>{generationInfo.sample_rate}Hz</span>
                    </div>
                  )}
                  {generationInfo.format && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">格式:</span>
                      <span>{generationInfo.format}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 使用说明 */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <i className="fas fa-question-circle mr-3 text-primary"></i>
                使用说明
              </h3>
              <div className="space-y-3 text-sm text-muted-foreground">
                <div className="flex items-start space-x-2">
                  <span className="flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary mt-0.5">1</span>
                  <span>在左侧输入您的歌词内容，支持结构化标签如 [verse], [chorus] 等</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary mt-0.5">2</span>
                  <span>选择音乐风格，可以上传参考音频或添加文本描述</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary mt-0.5">3</span>
                  <span>点击"生成歌曲"按钮，等待AI为您创作音乐</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center text-xs font-bold text-primary mt-0.5">4</span>
                  <span>生成完成后，您可以在音频播放器中试听和下载</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
