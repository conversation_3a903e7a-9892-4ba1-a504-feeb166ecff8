'use client';

import { useState, useRef, useEffect } from 'react';

interface AudioPlayerProps {
  src?: string;
  title?: string;
  artist?: string;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  onError?: (error: string) => void;
  className?: string;
}

export default function AudioPlayer({
  src,
  title = "生成的音乐",
  artist = "AI音乐创作",
  onLoadStart,
  onLoadEnd,
  onError,
  className = ""
}: AudioPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>("");
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadStart = () => {
      setIsLoading(true);
      setError("");
      onLoadStart?.();
    };

    const handleLoadedData = () => {
      setIsLoading(false);
      setDuration(audio.duration);
      onLoadEnd?.();
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    const handleError = () => {
      setIsLoading(false);
      const errorMsg = "音频加载失败";
      setError(errorMsg);
      onError?.(errorMsg);
    };

    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('loadeddata', handleLoadedData);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);

    return () => {
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('loadeddata', handleLoadedData);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
    };
  }, [onLoadStart, onLoadEnd, onError]);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio || !src) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleProgressClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const audio = audioRef.current;
    const progressBar = progressRef.current;
    if (!audio || !progressBar || !duration) return;

    const rect = progressBar.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;
    
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    setVolume(newVolume);
    
    const audio = audioRef.current;
    if (audio) {
      audio.volume = newVolume;
    }
  };

  const formatTime = (time: number): string => {
    if (isNaN(time)) return "0:00";
    
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  if (!src) {
    return (
      <div className={`audio-player p-6 text-center ${className}`}>
        <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-muted/20 rounded-full">
          <i className="fas fa-music text-2xl text-muted-foreground"></i>
        </div>
        <p className="text-muted-foreground">暂无音频文件</p>
        <p className="text-sm text-muted-foreground mt-1">生成音乐后将在此处播放</p>
      </div>
    );
  }

  return (
    <div className={`audio-player p-6 ${className}`}>
      <audio ref={audioRef} src={src} preload="metadata" />
      
      {error && (
        <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-600 text-sm">
          <i className="fas fa-exclamation-circle mr-2"></i>
          {error}
        </div>
      )}

      {/* 音乐信息 */}
      <div className="flex items-center mb-6">
        <div className="flex items-center justify-center w-12 h-12 bg-primary/20 rounded-lg mr-4">
          <i className="fas fa-music text-primary"></i>
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-lg">{title}</h3>
          <p className="text-sm text-muted-foreground">{artist}</p>
        </div>
        <div className="text-sm text-muted-foreground">
          {formatTime(currentTime)} / {formatTime(duration)}
        </div>
      </div>

      {/* 进度条 */}
      <div className="mb-6">
        <div
          ref={progressRef}
          className="relative h-2 bg-muted/30 rounded-full cursor-pointer group"
          onClick={handleProgressClick}
        >
          <div
            className="absolute top-0 left-0 h-full bg-gradient-to-r from-primary to-secondary rounded-full transition-all duration-150"
            style={{ width: `${progressPercentage}%` }}
          />
          <div
            className="absolute top-1/2 w-4 h-4 bg-white border-2 border-primary rounded-full transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity shadow-lg"
            style={{ left: `calc(${progressPercentage}% - 8px)` }}
          />
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="flex items-center justify-center space-x-4 mb-4">
        <button
          onClick={() => {
            const audio = audioRef.current;
            if (audio) {
              audio.currentTime = Math.max(0, audio.currentTime - 10);
            }
          }}
          className="p-2 rounded-full bg-muted/20 hover:bg-muted/40 transition-colors"
          disabled={!src || isLoading}
        >
          <i className="fas fa-backward text-lg"></i>
        </button>

        <button
          onClick={togglePlay}
          disabled={!src || isLoading}
          className="p-4 rounded-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <i className="fas fa-spinner fa-spin text-xl"></i>
          ) : isPlaying ? (
            <i className="fas fa-pause text-xl"></i>
          ) : (
            <i className="fas fa-play text-xl"></i>
          )}
        </button>

        <button
          onClick={() => {
            const audio = audioRef.current;
            if (audio) {
              audio.currentTime = Math.min(duration, audio.currentTime + 10);
            }
          }}
          className="p-2 rounded-full bg-muted/20 hover:bg-muted/40 transition-colors"
          disabled={!src || isLoading}
        >
          <i className="fas fa-forward text-lg"></i>
        </button>
      </div>

      {/* 音量控制 */}
      <div className="flex items-center space-x-3">
        <i className="fas fa-volume-down text-muted-foreground"></i>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={volume}
          onChange={handleVolumeChange}
          className="flex-1 h-1 bg-muted/30 rounded-lg appearance-none cursor-pointer"
        />
        <i className="fas fa-volume-up text-muted-foreground"></i>
        <span className="text-sm text-muted-foreground w-8">
          {Math.round(volume * 100)}%
        </span>
      </div>
    </div>
  );
}
