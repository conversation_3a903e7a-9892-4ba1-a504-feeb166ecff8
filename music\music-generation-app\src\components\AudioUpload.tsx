'use client';

import { useState, useRef, useCallback } from 'react';

interface AudioUploadProps {
  onFileSelect: (file: File | null) => void;
  accept?: string;
  maxSize?: number; // in MB
  className?: string;
}

export default function AudioUpload({
  onFileSelect,
  accept = "audio/*",
  maxSize = 10,
  className = ""
}: AudioUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    // 检查文件类型
    if (!file.type.startsWith('audio/')) {
      return '请选择音频文件';
    }

    // 检查文件大小
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSize) {
      return `文件大小不能超过 ${maxSize}MB`;
    }

    return null;
  };

  const handleFileSelect = useCallback((file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      setSelectedFile(null);
      onFileSelect(null);
      return;
    }

    setError("");
    setSelectedFile(file);
    onFileSelect(file);
  }, [maxSize, onFileSelect]);

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setError("");
    onFileSelect(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (duration: number): string => {
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 上传区域 */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200
          ${isDragOver 
            ? 'border-primary bg-primary/5' 
            : selectedFile 
              ? 'border-green-500 bg-green-500/5' 
              : 'border-muted hover:border-primary/50 hover:bg-primary/5'
          }
          ${error ? 'border-red-500 bg-red-500/5' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileInputChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />

        {selectedFile ? (
          <div className="space-y-3">
            <div className="flex items-center justify-center w-16 h-16 mx-auto bg-green-500/20 rounded-full">
              <i className="fas fa-music text-2xl text-green-500"></i>
            </div>
            <div>
              <p className="font-medium text-green-600">{selectedFile.name}</p>
              <p className="text-sm text-muted-foreground">
                {formatFileSize(selectedFile.size)}
              </p>
            </div>
            <button
              onClick={handleRemoveFile}
              className="inline-flex items-center px-3 py-1 text-sm bg-red-500/20 text-red-600 rounded-md hover:bg-red-500/30 transition-colors"
            >
              <i className="fas fa-trash mr-1"></i>
              移除文件
            </button>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center justify-center w-16 h-16 mx-auto bg-muted/20 rounded-full">
              <i className={`fas fa-cloud-upload-alt text-2xl ${isDragOver ? 'text-primary' : 'text-muted-foreground'}`}></i>
            </div>
            <div>
              <p className="text-lg font-medium">
                {isDragOver ? '释放文件以上传' : '拖拽音频文件到此处'}
              </p>
              <p className="text-sm text-muted-foreground">
                或 <span className="text-primary cursor-pointer hover:underline">点击选择文件</span>
              </p>
            </div>
            <div className="text-xs text-muted-foreground">
              支持格式：MP3, WAV, FLAC, AAC 等 • 最大 {maxSize}MB
            </div>
          </div>
        )}
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="flex items-center p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-600">
          <i className="fas fa-exclamation-circle mr-2"></i>
          {error}
        </div>
      )}

      {/* 上传提示 */}
      <div className="p-3 bg-accent/10 rounded-lg border border-accent/20">
        <div className="text-sm text-accent-foreground">
          <i className="fas fa-info-circle mr-2"></i>
          <strong>音频提示说明：</strong>
        </div>
        <div className="text-xs text-muted-foreground mt-1 space-y-1">
          <div>• 上传的音频文件将作为生成音乐的参考</div>
          <div>• 系统会分析音频的风格、节奏和音色特征</div>
          <div>• 建议上传高质量的音频文件以获得更好的效果</div>
          <div>• 音频时长建议在30秒到5分钟之间</div>
        </div>
      </div>
    </div>
  );
}
